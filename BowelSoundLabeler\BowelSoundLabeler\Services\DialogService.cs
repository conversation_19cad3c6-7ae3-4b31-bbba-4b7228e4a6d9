using Microsoft.Extensions.Logging;
using Microsoft.UI.Xaml.Controls;
using Windows.Storage;
using Windows.Storage.Pickers;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 对话框服务实现
    /// </summary>
    public class DialogService : IDialogService
    {
        private readonly ILogger<DialogService> _logger;

        public DialogService(ILogger<DialogService> logger)
        {
            _logger = logger;
        }

        public async Task<string?> SelectFolderAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FolderPicker();
                
                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                picker.SuggestedStartLocation = PickerLocationId.Desktop;
                picker.FileTypeFilter.Add("*");

                // 如果提供了初始文件夹，尝试设置
                if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                {
                    try
                    {
                        var folder = await StorageFolder.GetFolderFromPathAsync(initialFolder);
                        picker.SuggestedStartLocation = PickerLocationId.Unspecified;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "无法设置初始文件夹: {Folder}", initialFolder);
                    }
                }

                var selectedFolder = await picker.PickSingleFolderAsync();
                if (selectedFolder != null)
                {
                    _logger.LogInformation("选择了文件夹: {FolderPath}", selectedFolder.Path);
                    return selectedFolder.Path;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择文件夹时发生错误");
                throw;
            }
        }

        public async Task<bool> ShowConfirmationAsync(string title, string message)
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = title,
                    Content = message,
                    PrimaryButtonText = "确定",
                    SecondaryButtonText = "取消",
                    DefaultButton = ContentDialogButton.Primary,
                    XamlRoot = App.MainWindow.Content.XamlRoot
                };

                var result = await dialog.ShowAsync();
                return result == ContentDialogResult.Primary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示确认对话框时发生错误");
                throw;
            }
        }

        public async Task ShowInformationAsync(string title, string message)
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定",
                    DefaultButton = ContentDialogButton.Close,
                    XamlRoot = App.MainWindow.Content.XamlRoot
                };

                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示信息对话框时发生错误");
                throw;
            }
        }

        public async Task ShowErrorAsync(string title, string message)
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定",
                    DefaultButton = ContentDialogButton.Close,
                    XamlRoot = App.MainWindow.Content.XamlRoot
                };

                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示错误对话框时发生错误");
                throw;
            }
        }

        public async Task<string?> ShowInputAsync(string title, string message, string defaultValue = "")
        {
            try
            {
                var textBox = new TextBox
                {
                    Text = defaultValue,
                    SelectionStart = 0,
                    SelectionLength = defaultValue.Length
                };

                var dialog = new ContentDialog
                {
                    Title = title,
                    Content = new StackPanel
                    {
                        Children =
                        {
                            new TextBlock { Text = message, Margin = new Microsoft.UI.Xaml.Thickness(0, 0, 0, 10) },
                            textBox
                        }
                    },
                    PrimaryButtonText = "确定",
                    SecondaryButtonText = "取消",
                    DefaultButton = ContentDialogButton.Primary,
                    XamlRoot = App.MainWindow.Content.XamlRoot
                };

                var result = await dialog.ShowAsync();
                return result == ContentDialogResult.Primary ? textBox.Text : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示输入对话框时发生错误");
                throw;
            }
        }

        public async Task<DialogResult> ShowThreeChoiceAsync(string title, string message, string option1 = "是", string option2 = "否", string option3 = "跳过")
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = title,
                    Content = message,
                    PrimaryButtonText = option1,
                    SecondaryButtonText = option2,
                    CloseButtonText = option3,
                    DefaultButton = ContentDialogButton.Secondary,
                    XamlRoot = App.MainWindow.Content.XamlRoot
                };

                var result = await dialog.ShowAsync();
                return result switch
                {
                    ContentDialogResult.Primary => DialogResult.Option1,
                    ContentDialogResult.Secondary => DialogResult.Option2,
                    ContentDialogResult.None => DialogResult.Option3,
                    _ => DialogResult.Cancel
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示三选一对话框时发生错误");
                throw;
            }
        }
    }
}
