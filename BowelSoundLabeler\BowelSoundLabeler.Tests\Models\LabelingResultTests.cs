using BowelSoundLabeler.Models;
using Xunit;

namespace BowelSoundLabeler.Tests.Models
{
    public class LabelingResultTests
    {
        [Fact]
        public void GenerateNewFileName_NoResult_ShouldReturnOriginalName()
        {
            // Arrange
            var audioFile = CreateTestAudioFile("test_file.csv");
            var labelingInfo = new FileLabelingInfo
            {
                AudioFile = audioFile,
                Result = LabelingResult.Skip
            };

            // Act
            var newFileName = labelingInfo.GenerateNewFileName();

            // Assert
            Assert.Equal("test_file.csv", newFileName);
        }

        [Fact]
        public void GenerateNewFileName_NoLabelingResult_ShouldAddNoSuffix()
        {
            // Arrange
            var audioFile = CreateTestAudioFile("test_file.csv");
            var labelingInfo = new FileLabelingInfo
            {
                AudioFile = audioFile,
                Result = LabelingResult.No
            };

            // Act
            var newFileName = labelingInfo.GenerateNewFileName();

            // Assert
            Assert.Equal("test_file_no.csv", newFileName);
        }

        [Theory]
        [InlineData(1, "test_file_yes_1.csv")]
        [InlineData(5, "test_file_yes_5.csv")]
        [InlineData(0, "test_file_yes_0.csv")]
        public void GenerateNewFileName_YesLabelingResult_ShouldAddYesSuffixWithCount(int count, string expected)
        {
            // Arrange
            var audioFile = CreateTestAudioFile("test_file.csv");
            var labelingInfo = new FileLabelingInfo
            {
                AudioFile = audioFile,
                Result = LabelingResult.Yes,
                BowelSoundCount = count
            };

            // Act
            var newFileName = labelingInfo.GenerateNewFileName();

            // Assert
            Assert.Equal(expected, newFileName);
        }

        [Fact]
        public void GenerateNewFilePath_ShouldReturnCorrectPath()
        {
            // Arrange
            var tempDir = Path.GetTempPath();
            var originalPath = Path.Combine(tempDir, "test_file.csv");
            var audioFile = CreateTestAudioFile(originalPath);
            var labelingInfo = new FileLabelingInfo
            {
                AudioFile = audioFile,
                Result = LabelingResult.Yes,
                BowelSoundCount = 3
            };

            // Act
            var newFilePath = labelingInfo.GenerateNewFilePath();

            // Assert
            var expectedPath = Path.Combine(tempDir, "test_file_yes_3.csv");
            Assert.Equal(expectedPath, newFilePath);
        }

        [Theory]
        [InlineData("file.csv", LabelingResult.No, 0, "file_no.csv")]
        [InlineData("data.wav", LabelingResult.Yes, 2, "data_yes_2.wav")]
        [InlineData("signal.mat", LabelingResult.Skip, 0, "signal.mat")]
        public void GenerateNewFileName_VariousScenarios_ShouldGenerateCorrectNames(
            string originalName, LabelingResult result, int count, string expected)
        {
            // Arrange
            var audioFile = CreateTestAudioFile(originalName);
            var labelingInfo = new FileLabelingInfo
            {
                AudioFile = audioFile,
                Result = result,
                BowelSoundCount = count
            };

            // Act
            var newFileName = labelingInfo.GenerateNewFileName();

            // Assert
            Assert.Equal(expected, newFileName);
        }

        private AudioFile CreateTestAudioFile(string filePath)
        {
            return new AudioFile
            {
                FilePath = filePath,
                FileName = Path.GetFileNameWithoutExtension(filePath),
                FileExtension = Path.GetExtension(filePath),
                FileSize = 1024,
                CreatedTime = DateTime.Now,
                ModifiedTime = DateTime.Now,
                IsValid = true
            };
        }
    }
}
