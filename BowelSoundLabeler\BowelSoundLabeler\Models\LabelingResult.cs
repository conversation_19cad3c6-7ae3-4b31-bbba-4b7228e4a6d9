namespace BowelSoundLabeler.Models
{
    /// <summary>
    /// 标注结果枚举
    /// </summary>
    public enum LabelingResult
    {
        /// <summary>
        /// 跳过
        /// </summary>
        Skip,
        /// <summary>
        /// 无肠鸣音
        /// </summary>
        No,
        /// <summary>
        /// 有肠鸣音
        /// </summary>
        Yes
    }

    /// <summary>
    /// 文件标注信息
    /// </summary>
    public class FileLabelingInfo
    {
        public AudioFile AudioFile { get; set; } = null!;
        public LabelingResult Result { get; set; }
        public int BowelSoundCount { get; set; }
        public bool IsProcessed { get; set; }
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 生成新的文件名
        /// </summary>
        public string GenerateNewFileName()
        {
            var baseName = AudioFile.FileName;
            var extension = AudioFile.FileExtension;

            return Result switch
            {
                LabelingResult.No => $"{baseName}_no{extension}",
                LabelingResult.Yes => $"{baseName}_yes_{BowelSoundCount}{extension}",
                _ => $"{baseName}{extension}"
            };
        }

        /// <summary>
        /// 获取新的完整文件路径
        /// </summary>
        public string GenerateNewFilePath()
        {
            var directory = Path.GetDirectoryName(AudioFile.FilePath) ?? string.Empty;
            var newFileName = GenerateNewFileName();
            return Path.Combine(directory, newFileName);
        }
    }
}
