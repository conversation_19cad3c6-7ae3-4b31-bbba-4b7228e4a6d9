using System.Text;
using Microsoft.Extensions.Logging;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// CSV文件处理服务实现
    /// </summary>
    public class CsvProcessingService : ICsvProcessingService
    {
        private readonly ILogger<CsvProcessingService> _logger;
        private readonly IFileService _fileService;

        public CsvProcessingService(ILogger<CsvProcessingService> logger, IFileService fileService)
        {
            _logger = logger;
            _fileService = fileService;
        }

        public async Task<bool> ValidateCsvFileAsync(AudioFile csvFile)
        {
            try
            {
                if (!File.Exists(csvFile.FilePath))
                {
                    csvFile.ErrorMessage = "文件不存在";
                    return false;
                }

                if (!string.Equals(csvFile.FileExtension, ".csv", StringComparison.OrdinalIgnoreCase))
                {
                    csvFile.ErrorMessage = "不是CSV文件格式";
                    return false;
                }

                // 尝试读取文件的前几行来验证格式
                using var reader = new StreamReader(csvFile.FilePath, Encoding.UTF8);
                var firstLine = await reader.ReadLineAsync();
                
                if (string.IsNullOrEmpty(firstLine))
                {
                    csvFile.ErrorMessage = "CSV文件为空";
                    return false;
                }

                csvFile.IsValid = true;
                csvFile.ErrorMessage = null;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证CSV文件时发生错误: {FilePath}", csvFile.FilePath);
                csvFile.IsValid = false;
                csvFile.ErrorMessage = ex.Message;
                return false;
            }
        }

        public async Task<CsvFileInfo?> GetCsvInfoAsync(AudioFile csvFile)
        {
            try
            {
                if (!await ValidateCsvFileAsync(csvFile))
                {
                    return null;
                }

                var info = new CsvFileInfo { IsValid = true };
                var lines = await File.ReadAllLinesAsync(csvFile.FilePath, Encoding.UTF8);
                
                info.RowCount = lines.Length;
                
                if (lines.Length > 0)
                {
                    var firstLine = lines[0];
                    var delimiter = DetectDelimiter(firstLine);
                    info.Delimiter = delimiter;
                    
                    var columns = SplitCsvLine(firstLine, delimiter);
                    info.ColumnCount = columns.Count;
                    info.Headers = columns;
                    info.HasHeaders = true; // 假设第一行是标题
                }

                info.Encoding = "UTF-8";
                return info;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取CSV文件信息时发生错误: {FilePath}", csvFile.FilePath);
                return new CsvFileInfo 
                { 
                    IsValid = false, 
                    ErrorMessage = ex.Message 
                };
            }
        }

        public async Task<CsvPreview?> GetCsvPreviewAsync(AudioFile csvFile, int maxRows = 10)
        {
            try
            {
                var info = await GetCsvInfoAsync(csvFile);
                if (info == null || !info.IsValid)
                {
                    return null;
                }

                var preview = new CsvPreview
                {
                    Headers = info.Headers,
                    TotalRows = info.RowCount
                };

                var lines = await File.ReadAllLinesAsync(csvFile.FilePath, Encoding.UTF8);
                var rowsToRead = Math.Min(maxRows + 1, lines.Length); // +1 for header
                
                for (int i = 1; i < rowsToRead && i < lines.Length; i++) // Skip header
                {
                    var columns = SplitCsvLine(lines[i], info.Delimiter);
                    preview.Rows.Add(columns);
                }

                preview.HasMoreRows = lines.Length > maxRows + 1;
                return preview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取CSV文件预览时发生错误: {FilePath}", csvFile.FilePath);
                return null;
            }
        }

        public async Task<bool> ProcessCsvLabelingAsync(FileLabelingInfo labelingInfo, bool overwrite = false)
        {
            try
            {
                var oldPath = labelingInfo.AudioFile.FilePath;
                var newPath = labelingInfo.GenerateNewFilePath();

                if (await _fileService.FileExistsAsync(newPath) && !overwrite)
                {
                    throw new InvalidOperationException($"目标文件已存在: {newPath}");
                }

                await _fileService.RenameFileAsync(oldPath, newPath, overwrite);
                labelingInfo.IsProcessed = true;
                
                _logger.LogInformation("CSV文件标注处理成功: {OldPath} -> {NewPath}", oldPath, newPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理CSV文件标注时发生错误: {FilePath}", labelingInfo.AudioFile.FilePath);
                labelingInfo.ErrorMessage = ex.Message;
                return false;
            }
        }

        public async Task<BatchProcessingResult> ProcessBatchCsvLabelingAsync(
            IList<FileLabelingInfo> labelingInfos, 
            IProgress<BatchProcessingProgress>? progress = null,
            CancellationToken cancellationToken = default)
        {
            var result = new BatchProcessingResult
            {
                TotalFiles = labelingInfos.Count
            };

            try
            {
                for (int i = 0; i < labelingInfos.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        result.IsCancelled = true;
                        break;
                    }

                    var labelingInfo = labelingInfos[i];
                    
                    // 报告进度
                    progress?.Report(new BatchProcessingProgress
                    {
                        CurrentIndex = i + 1,
                        TotalCount = labelingInfos.Count,
                        CurrentFileName = labelingInfo.AudioFile.DisplayName,
                        Status = "正在处理..."
                    });

                    try
                    {
                        if (labelingInfo.Result == LabelingResult.Skip)
                        {
                            result.SkippedFiles++;
                            continue;
                        }

                        var success = await ProcessCsvLabelingAsync(labelingInfo, true);
                        if (success)
                        {
                            result.ProcessedFiles++;
                        }
                        else
                        {
                            result.ErrorFiles++;
                            if (!string.IsNullOrEmpty(labelingInfo.ErrorMessage))
                            {
                                result.Errors.Add($"{labelingInfo.AudioFile.DisplayName}: {labelingInfo.ErrorMessage}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        result.ErrorFiles++;
                        result.Errors.Add($"{labelingInfo.AudioFile.DisplayName}: {ex.Message}");
                        _logger.LogError(ex, "批量处理文件时发生错误: {FilePath}", labelingInfo.AudioFile.FilePath);
                    }
                }

                result.IsCompleted = !result.IsCancelled;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量处理CSV文件时发生错误");
                result.Errors.Add($"批量处理失败: {ex.Message}");
                return result;
            }
        }

        private char DetectDelimiter(string line)
        {
            var delimiters = new[] { ',', ';', '\t', '|' };
            var maxCount = 0;
            var bestDelimiter = ',';

            foreach (var delimiter in delimiters)
            {
                var count = line.Count(c => c == delimiter);
                if (count > maxCount)
                {
                    maxCount = count;
                    bestDelimiter = delimiter;
                }
            }

            return bestDelimiter;
        }

        private List<string> SplitCsvLine(string line, char delimiter)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            var inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == delimiter && !inQuotes)
                {
                    result.Add(current.ToString().Trim());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString().Trim());
            return result;
        }
    }
}
