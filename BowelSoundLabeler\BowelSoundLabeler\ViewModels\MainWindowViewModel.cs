using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using BowelSoundLabeler.Models;
using BowelSoundLabeler.Services;

namespace BowelSoundLabeler.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly ILogger<MainWindowViewModel> _logger;
        private readonly ISettingsService _settingsService;
        private readonly IFileService _fileService;
        private readonly IDialogService _dialogService;
        private readonly ILabelingService _labelingService;

        [ObservableProperty]
        private string _statusText = "就绪";

        [ObservableProperty]
        private string _currentFolderText = "当前默认文件夹: 未设置";

        [ObservableProperty]
        private bool _isProcessing = false;

        [ObservableProperty]
        private double _progressPercentage = 0;

        [ObservableProperty]
        private bool _showProgress = false;

        [ObservableProperty]
        private bool _canStartLabeling = false;

        public ObservableCollection<AudioFile> SelectedFiles { get; } = new();

        public MainWindowViewModel(
            ILogger<MainWindowViewModel> logger,
            ISettingsService settingsService,
            IFileService fileService,
            IDialogService dialogService,
            ILabelingService labelingService)
        {
            _logger = logger;
            _settingsService = settingsService;
            _fileService = fileService;
            _dialogService = dialogService;
            _labelingService = labelingService;

            // 初始化时更新默认文件夹显示
            _ = UpdateCurrentFolderDisplayAsync();
        }

        [RelayCommand]
        private async Task SetDefaultFolderAsync()
        {
            try
            {
                StatusText = "请选择默认文件夹...";

                var currentDefault = await _settingsService.GetDefaultFolderAsync();
                var folderPath = await _dialogService.SelectFolderAsync(currentDefault);

                if (!string.IsNullOrEmpty(folderPath))
                {
                    await _settingsService.SetDefaultFolderAsync(folderPath);
                    await UpdateCurrentFolderDisplayAsync();
                    StatusText = "默认文件夹已更新";
                }
                else
                {
                    StatusText = "未选择文件夹";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置默认文件夹时发生错误");
                StatusText = $"设置默认文件夹失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task ClearDefaultFolderAsync()
        {
            try
            {
                await _settingsService.ClearDefaultFolderAsync();
                await UpdateCurrentFolderDisplayAsync();
                StatusText = "默认文件夹设置已清除";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除默认文件夹时发生错误");
                StatusText = $"清除默认文件夹失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task SelectSingleFileAsync()
        {
            try
            {
                StatusText = "正在选择文件...";
                
                var defaultFolder = await _settingsService.GetDefaultFolderAsync();
                var audioFile = await _fileService.SelectSingleFileAsync(defaultFolder);
                
                if (audioFile != null)
                {
                    SelectedFiles.Clear();
                    SelectedFiles.Add(audioFile);
                    
                    // 保存用户选择的文件夹作为新的默认路径
                    var folderPath = Path.GetDirectoryName(audioFile.FilePath);
                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        await _settingsService.SetDefaultFolderAsync(folderPath);
                        await UpdateCurrentFolderDisplayAsync();
                    }
                    
                    CanStartLabeling = true;
                    StatusText = "已选择1个文件";
                }
                else
                {
                    StatusText = "未选择文件";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择单个文件时发生错误");
                StatusText = $"选择文件失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task SelectMultipleFilesAsync()
        {
            try
            {
                StatusText = "正在选择文件...";
                
                var defaultFolder = await _settingsService.GetDefaultFolderAsync();
                var audioFiles = await _fileService.SelectMultipleFilesAsync(defaultFolder);
                
                if (audioFiles.Count > 0)
                {
                    SelectedFiles.Clear();
                    foreach (var file in audioFiles)
                    {
                        SelectedFiles.Add(file);
                    }
                    
                    // 保存用户选择的文件夹作为新的默认路径
                    var folderPath = Path.GetDirectoryName(audioFiles[0].FilePath);
                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        await _settingsService.SetDefaultFolderAsync(folderPath);
                        await UpdateCurrentFolderDisplayAsync();
                    }
                    
                    CanStartLabeling = true;
                    StatusText = $"已选择{audioFiles.Count}个文件";
                }
                else
                {
                    StatusText = "未选择文件";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择多个文件时发生错误");
                StatusText = $"选择文件失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private void ClearFileList()
        {
            try
            {
                SelectedFiles.Clear();
                CanStartLabeling = false;
                StatusText = "已清空文件列表";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空文件列表时发生错误");
                StatusText = $"清空文件列表失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private void RemoveSelectedFile(AudioFile audioFile)
        {
            try
            {
                if (SelectedFiles.Contains(audioFile))
                {
                    SelectedFiles.Remove(audioFile);
                    CanStartLabeling = SelectedFiles.Count > 0;
                    StatusText = $"已移除文件: {audioFile.DisplayName}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除文件时发生错误");
                StatusText = $"移除文件失败: {ex.Message}";
            }
        }

        [RelayCommand(CanExecute = nameof(CanStartLabeling))]
        private async Task StartLabelingAsync()
        {
            try
            {
                IsProcessing = true;
                StatusText = "开始标注...";

                var files = SelectedFiles.ToList();
                var progress = new Progress<BatchLabelingProgress>(OnLabelingProgress);

                var result = await _labelingService.StartBatchLabelingAsync(files, progress);

                // 显示完成信息
                var resultMsg = $"标注完成！\n\n处理成功: {result.ProcessedFiles} 个文件\n跳过: {result.SkippedFiles} 个文件\n错误: {result.ErrorFiles} 个文件\n耗时: {result.Duration:mm\\:ss}";

                if (result.Errors.Count > 0)
                {
                    resultMsg += $"\n\n错误详情:\n{string.Join("\n", result.Errors.Take(5))}";
                    if (result.Errors.Count > 5)
                    {
                        resultMsg += $"\n... 还有 {result.Errors.Count - 5} 个错误";
                    }
                }

                await _dialogService.ShowInformationAsync("标注完成", resultMsg);

                // 清空文件列表
                SelectedFiles.Clear();
                CanStartLabeling = false;
                StatusText = "标注完成，就绪";
                ShowProgress = false;
                ProgressPercentage = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始标注时发生错误");
                StatusText = $"标注失败: {ex.Message}";
                await _dialogService.ShowErrorAsync("标注失败", ex.Message);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        private void OnLabelingProgress(BatchLabelingProgress progress)
        {
            StatusText = $"正在处理文件 {progress.CurrentIndex}/{progress.TotalCount}: {progress.CurrentFileName} - {progress.Status}";
            ProgressPercentage = progress.ProgressPercentage;
            ShowProgress = true;
        }

        private async Task UpdateCurrentFolderDisplayAsync()
        {
            try
            {
                var defaultFolder = await _settingsService.GetDefaultFolderAsync();
                if (!string.IsNullOrEmpty(defaultFolder))
                {
                    // 如果路径太长，截断显示
                    var displayPath = defaultFolder.Length > 60 
                        ? "..." + defaultFolder.Substring(defaultFolder.Length - 57)
                        : defaultFolder;
                    CurrentFolderText = $"当前默认文件夹: {displayPath}";
                }
                else
                {
                    CurrentFolderText = "当前默认文件夹: 未设置";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新当前文件夹显示时发生错误");
                CurrentFolderText = "当前默认文件夹: 获取失败";
            }
        }
    }
}
