﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>DESKTOP-PMTGQNT</Machine>
    <WindowsUser>jie</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|x64</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>098f3ffc-15b4-4f7b-9d9f-bc282c5820ae</PackageIdentityName>
    <PackageIdentityPublisher>CN=jie</PackageIdentityPublisher>
    <IntermediateOutputPath>D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath>
    </PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\AppX</LayoutDir>
    <RegisteredManifestChecksum>E71539494019AF646501382CAC4E43A51EAB982AB5774147A130166AD8A1280E</RegisteredManifestChecksum>
    <RegisteredPackageMoniker>098f3ffc-15b4-4f7b-9d9f-bc282c5820ae_1.0.0.0_x64__9cwank7p956z0</RegisteredPackageMoniker>
    <RegisteredUserModeAppID>098f3ffc-15b4-4f7b-9d9f-bc282c5820ae_9cwank7p956z0!App</RegisteredUserModeAppID>
    <RegisteredPackageID>098f3ffc-15b4-4f7b-9d9f-bc282c5820ae</RegisteredPackageID>
    <RegisteredPackagePublisher>CN=jie</RegisteredPackagePublisher>
    <RegisteredVersion>1.0.0.0</RegisteredVersion>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-08-24T03:05:57.589</Modified>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
      <Modified>2025-08-24T02:37:09.972</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\BowelSoundLabeler.runtimeconfig.json">
      <PackagePath>BowelSoundLabeler.runtimeconfig.json</PackagePath>
      <Modified>2025-08-24T02:55:51.945</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\BowelSoundLabeler.dll">
      <PackagePath>BowelSoundLabeler.dll</PackagePath>
      <Modified>2025-08-24T03:05:56.760</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\apphost.exe">
      <PackagePath>BowelSoundLabeler.exe</PackagePath>
      <Modified>2025-08-24T03:05:56.768</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\BowelSoundLabeler.deps.json">
      <PackagePath>BowelSoundLabeler.deps.json</PackagePath>
      <Modified>2025-08-24T02:55:51.927</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.CSharp.dll">
      <PackagePath>Microsoft.CSharp.dll</PackagePath>
      <Modified>2025-07-15T18:06:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.Core.dll">
      <PackagePath>Microsoft.VisualBasic.Core.dll</PackagePath>
      <Modified>2025-07-15T18:06:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Primitives.dll">
      <PackagePath>Microsoft.Win32.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:00:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Registry.dll">
      <PackagePath>Microsoft.Win32.Registry.dll</PackagePath>
      <Modified>2025-07-15T18:06:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.AppContext.dll">
      <PackagePath>System.AppContext.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Buffers.dll">
      <PackagePath>System.Buffers.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Concurrent.dll">
      <PackagePath>System.Collections.Concurrent.dll</PackagePath>
      <Modified>2025-07-15T18:07:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Immutable.dll">
      <PackagePath>System.Collections.Immutable.dll</PackagePath>
      <Modified>2025-07-15T18:07:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.NonGeneric.dll">
      <PackagePath>System.Collections.NonGeneric.dll</PackagePath>
      <Modified>2025-07-15T18:07:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Specialized.dll">
      <PackagePath>System.Collections.Specialized.dll</PackagePath>
      <Modified>2025-07-15T18:07:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.dll">
      <PackagePath>System.Collections.dll</PackagePath>
      <Modified>2025-07-15T18:07:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.Annotations.dll">
      <PackagePath>System.ComponentModel.Annotations.dll</PackagePath>
      <Modified>2025-07-15T18:07:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.DataAnnotations.dll">
      <PackagePath>System.ComponentModel.DataAnnotations.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.EventBasedAsync.dll">
      <PackagePath>System.ComponentModel.EventBasedAsync.dll</PackagePath>
      <Modified>2025-07-15T18:07:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.Primitives.dll">
      <PackagePath>System.ComponentModel.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:07:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.TypeConverter.dll">
      <PackagePath>System.ComponentModel.TypeConverter.dll</PackagePath>
      <Modified>2025-07-15T18:07:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.dll">
      <PackagePath>System.ComponentModel.dll</PackagePath>
      <Modified>2025-07-15T18:07:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Configuration.dll">
      <PackagePath>System.Configuration.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Console.dll">
      <PackagePath>System.Console.dll</PackagePath>
      <Modified>2025-07-15T18:07:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Core.dll">
      <PackagePath>System.Core.dll</PackagePath>
      <Modified>2025-07-15T18:01:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.Common.dll">
      <PackagePath>System.Data.Common.dll</PackagePath>
      <Modified>2025-07-15T18:07:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.DataSetExtensions.dll">
      <PackagePath>System.Data.DataSetExtensions.dll</PackagePath>
      <Modified>2025-07-15T18:01:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.dll">
      <PackagePath>System.Data.dll</PackagePath>
      <Modified>2025-07-15T18:01:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Contracts.dll">
      <PackagePath>System.Diagnostics.Contracts.dll</PackagePath>
      <Modified>2025-07-15T18:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Debug.dll">
      <PackagePath>System.Diagnostics.Debug.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.DiagnosticSource.dll">
      <PackagePath>System.Diagnostics.DiagnosticSource.dll</PackagePath>
      <Modified>2025-07-15T18:07:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.FileVersionInfo.dll">
      <PackagePath>System.Diagnostics.FileVersionInfo.dll</PackagePath>
      <Modified>2025-07-15T18:07:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Process.dll">
      <PackagePath>System.Diagnostics.Process.dll</PackagePath>
      <Modified>2025-07-15T18:07:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.StackTrace.dll">
      <PackagePath>System.Diagnostics.StackTrace.dll</PackagePath>
      <Modified>2025-07-15T18:07:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.TextWriterTraceListener.dll">
      <PackagePath>System.Diagnostics.TextWriterTraceListener.dll</PackagePath>
      <Modified>2025-07-15T18:07:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Tools.dll">
      <PackagePath>System.Diagnostics.Tools.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.TraceSource.dll">
      <PackagePath>System.Diagnostics.TraceSource.dll</PackagePath>
      <Modified>2025-07-15T18:07:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Tracing.dll">
      <PackagePath>System.Diagnostics.Tracing.dll</PackagePath>
      <Modified>2025-07-15T17:59:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.Primitives.dll">
      <PackagePath>System.Drawing.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:07:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Dynamic.Runtime.dll">
      <PackagePath>System.Dynamic.Runtime.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Formats.Asn1.dll">
      <PackagePath>System.Formats.Asn1.dll</PackagePath>
      <Modified>2025-07-15T18:07:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Formats.Tar.dll">
      <PackagePath>System.Formats.Tar.dll</PackagePath>
      <Modified>2025-07-15T18:08:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.Calendars.dll">
      <PackagePath>System.Globalization.Calendars.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.Extensions.dll">
      <PackagePath>System.Globalization.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.dll">
      <PackagePath>System.Globalization.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.Brotli.dll">
      <PackagePath>System.IO.Compression.Brotli.dll</PackagePath>
      <Modified>2025-07-15T18:08:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.FileSystem.dll">
      <PackagePath>System.IO.Compression.FileSystem.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.ZipFile.dll">
      <PackagePath>System.IO.Compression.ZipFile.dll</PackagePath>
      <Modified>2025-07-15T18:08:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.dll">
      <PackagePath>System.IO.Compression.dll</PackagePath>
      <Modified>2025-07-15T18:08:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.AccessControl.dll">
      <PackagePath>System.IO.FileSystem.AccessControl.dll</PackagePath>
      <Modified>2025-07-15T18:08:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.DriveInfo.dll">
      <PackagePath>System.IO.FileSystem.DriveInfo.dll</PackagePath>
      <Modified>2025-07-15T18:08:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.Primitives.dll">
      <PackagePath>System.IO.FileSystem.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.Watcher.dll">
      <PackagePath>System.IO.FileSystem.Watcher.dll</PackagePath>
      <Modified>2025-07-15T18:08:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.dll">
      <PackagePath>System.IO.FileSystem.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.IsolatedStorage.dll">
      <PackagePath>System.IO.IsolatedStorage.dll</PackagePath>
      <Modified>2025-07-15T18:08:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.MemoryMappedFiles.dll">
      <PackagePath>System.IO.MemoryMappedFiles.dll</PackagePath>
      <Modified>2025-07-15T18:08:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Pipes.AccessControl.dll">
      <PackagePath>System.IO.Pipes.AccessControl.dll</PackagePath>
      <Modified>2025-07-15T17:58:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Pipes.dll">
      <PackagePath>System.IO.Pipes.dll</PackagePath>
      <Modified>2025-07-15T18:08:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.UnmanagedMemoryStream.dll">
      <PackagePath>System.IO.UnmanagedMemoryStream.dll</PackagePath>
      <Modified>2025-07-15T18:01:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.dll">
      <PackagePath>System.IO.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Expressions.dll">
      <PackagePath>System.Linq.Expressions.dll</PackagePath>
      <Modified>2025-07-15T18:08:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Parallel.dll">
      <PackagePath>System.Linq.Parallel.dll</PackagePath>
      <Modified>2025-07-15T18:08:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Queryable.dll">
      <PackagePath>System.Linq.Queryable.dll</PackagePath>
      <Modified>2025-07-15T18:08:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.dll">
      <PackagePath>System.Linq.dll</PackagePath>
      <Modified>2025-07-15T18:08:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Memory.dll">
      <PackagePath>System.Memory.dll</PackagePath>
      <Modified>2025-07-15T18:08:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Http.Json.dll">
      <PackagePath>System.Net.Http.Json.dll</PackagePath>
      <Modified>2025-07-15T18:08:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Http.dll">
      <PackagePath>System.Net.Http.dll</PackagePath>
      <Modified>2025-07-15T18:08:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.HttpListener.dll">
      <PackagePath>System.Net.HttpListener.dll</PackagePath>
      <Modified>2025-07-15T18:08:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Mail.dll">
      <PackagePath>System.Net.Mail.dll</PackagePath>
      <Modified>2025-07-15T18:09:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.NameResolution.dll">
      <PackagePath>System.Net.NameResolution.dll</PackagePath>
      <Modified>2025-07-15T18:09:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.NetworkInformation.dll">
      <PackagePath>System.Net.NetworkInformation.dll</PackagePath>
      <Modified>2025-07-15T18:09:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Ping.dll">
      <PackagePath>System.Net.Ping.dll</PackagePath>
      <Modified>2025-07-15T18:09:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Primitives.dll">
      <PackagePath>System.Net.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:09:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Quic.dll">
      <PackagePath>System.Net.Quic.dll</PackagePath>
      <Modified>2025-07-15T18:09:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Requests.dll">
      <PackagePath>System.Net.Requests.dll</PackagePath>
      <Modified>2025-07-15T18:09:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Security.dll">
      <PackagePath>System.Net.Security.dll</PackagePath>
      <Modified>2025-07-15T18:09:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.ServicePoint.dll">
      <PackagePath>System.Net.ServicePoint.dll</PackagePath>
      <Modified>2025-07-15T18:09:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Sockets.dll">
      <PackagePath>System.Net.Sockets.dll</PackagePath>
      <Modified>2025-07-15T18:09:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebClient.dll">
      <PackagePath>System.Net.WebClient.dll</PackagePath>
      <Modified>2025-07-15T18:09:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebHeaderCollection.dll">
      <PackagePath>System.Net.WebHeaderCollection.dll</PackagePath>
      <Modified>2025-07-15T18:09:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebProxy.dll">
      <PackagePath>System.Net.WebProxy.dll</PackagePath>
      <Modified>2025-07-15T18:09:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebSockets.Client.dll">
      <PackagePath>System.Net.WebSockets.Client.dll</PackagePath>
      <Modified>2025-07-15T18:09:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebSockets.dll">
      <PackagePath>System.Net.WebSockets.dll</PackagePath>
      <Modified>2025-07-15T18:09:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.dll">
      <PackagePath>System.Net.dll</PackagePath>
      <Modified>2025-07-15T18:01:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Numerics.Vectors.dll">
      <PackagePath>System.Numerics.Vectors.dll</PackagePath>
      <Modified>2025-07-15T18:01:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Numerics.dll">
      <PackagePath>System.Numerics.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ObjectModel.dll">
      <PackagePath>System.ObjectModel.dll</PackagePath>
      <Modified>2025-07-15T18:09:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.CoreLib.dll">
      <PackagePath>System.Private.CoreLib.dll</PackagePath>
      <Modified>2025-07-15T17:54:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.DataContractSerialization.dll">
      <PackagePath>System.Private.DataContractSerialization.dll</PackagePath>
      <Modified>2025-07-15T18:09:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Uri.dll">
      <PackagePath>System.Private.Uri.dll</PackagePath>
      <Modified>2025-07-15T18:09:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Xml.Linq.dll">
      <PackagePath>System.Private.Xml.Linq.dll</PackagePath>
      <Modified>2025-07-15T18:09:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Xml.dll">
      <PackagePath>System.Private.Xml.dll</PackagePath>
      <Modified>2025-07-15T18:09:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.DispatchProxy.dll">
      <PackagePath>System.Reflection.DispatchProxy.dll</PackagePath>
      <Modified>2025-07-15T18:10:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.ILGeneration.dll">
      <PackagePath>System.Reflection.Emit.ILGeneration.dll</PackagePath>
      <Modified>2025-07-15T18:00:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.Lightweight.dll">
      <PackagePath>System.Reflection.Emit.Lightweight.dll</PackagePath>
      <Modified>2025-07-15T18:00:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.dll">
      <PackagePath>System.Reflection.Emit.dll</PackagePath>
      <Modified>2025-07-15T18:10:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Extensions.dll">
      <PackagePath>System.Reflection.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Metadata.dll">
      <PackagePath>System.Reflection.Metadata.dll</PackagePath>
      <Modified>2025-07-15T18:10:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Primitives.dll">
      <PackagePath>System.Reflection.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:00:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.TypeExtensions.dll">
      <PackagePath>System.Reflection.TypeExtensions.dll</PackagePath>
      <Modified>2025-07-15T18:10:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.dll">
      <PackagePath>System.Reflection.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Reader.dll">
      <PackagePath>System.Resources.Reader.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.ResourceManager.dll">
      <PackagePath>System.Resources.ResourceManager.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Writer.dll">
      <PackagePath>System.Resources.Writer.dll</PackagePath>
      <Modified>2025-07-15T18:10:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.CompilerServices.Unsafe.dll">
      <PackagePath>System.Runtime.CompilerServices.Unsafe.dll</PackagePath>
      <Modified>2025-07-15T18:00:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.CompilerServices.VisualC.dll">
      <PackagePath>System.Runtime.CompilerServices.VisualC.dll</PackagePath>
      <Modified>2025-07-15T18:10:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Extensions.dll">
      <PackagePath>System.Runtime.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Handles.dll">
      <PackagePath>System.Runtime.Handles.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.JavaScript.dll">
      <PackagePath>System.Runtime.InteropServices.JavaScript.dll</PackagePath>
      <Modified>2025-07-15T18:10:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll">
      <PackagePath>System.Runtime.InteropServices.RuntimeInformation.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.dll">
      <PackagePath>System.Runtime.InteropServices.dll</PackagePath>
      <Modified>2025-07-15T18:10:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Intrinsics.dll">
      <PackagePath>System.Runtime.Intrinsics.dll</PackagePath>
      <Modified>2025-07-15T18:00:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Loader.dll">
      <PackagePath>System.Runtime.Loader.dll</PackagePath>
      <Modified>2025-07-15T18:00:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Numerics.dll">
      <PackagePath>System.Runtime.Numerics.dll</PackagePath>
      <Modified>2025-07-15T18:10:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Formatters.dll">
      <PackagePath>System.Runtime.Serialization.Formatters.dll</PackagePath>
      <Modified>2025-07-15T18:10:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Json.dll">
      <PackagePath>System.Runtime.Serialization.Json.dll</PackagePath>
      <Modified>2025-07-15T18:00:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Primitives.dll">
      <PackagePath>System.Runtime.Serialization.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:10:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Xml.dll">
      <PackagePath>System.Runtime.Serialization.Xml.dll</PackagePath>
      <Modified>2025-07-15T18:00:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.dll">
      <PackagePath>System.Runtime.Serialization.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.dll">
      <PackagePath>System.Runtime.dll</PackagePath>
      <Modified>2025-07-15T18:00:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.AccessControl.dll">
      <PackagePath>System.Security.AccessControl.dll</PackagePath>
      <Modified>2025-07-15T18:10:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Claims.dll">
      <PackagePath>System.Security.Claims.dll</PackagePath>
      <Modified>2025-07-15T18:10:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Algorithms.dll">
      <PackagePath>System.Security.Cryptography.Algorithms.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Cng.dll">
      <PackagePath>System.Security.Cryptography.Cng.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Csp.dll">
      <PackagePath>System.Security.Cryptography.Csp.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Encoding.dll">
      <PackagePath>System.Security.Cryptography.Encoding.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.OpenSsl.dll">
      <PackagePath>System.Security.Cryptography.OpenSsl.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Primitives.dll">
      <PackagePath>System.Security.Cryptography.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.X509Certificates.dll">
      <PackagePath>System.Security.Cryptography.X509Certificates.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.dll">
      <PackagePath>System.Security.Cryptography.dll</PackagePath>
      <Modified>2025-07-15T18:10:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Principal.Windows.dll">
      <PackagePath>System.Security.Principal.Windows.dll</PackagePath>
      <Modified>2025-07-15T18:10:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Principal.dll">
      <PackagePath>System.Security.Principal.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.SecureString.dll">
      <PackagePath>System.Security.SecureString.dll</PackagePath>
      <Modified>2025-07-15T18:01:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.dll">
      <PackagePath>System.Security.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ServiceModel.Web.dll">
      <PackagePath>System.ServiceModel.Web.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ServiceProcess.dll">
      <PackagePath>System.ServiceProcess.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.CodePages.dll">
      <PackagePath>System.Text.Encoding.CodePages.dll</PackagePath>
      <Modified>2025-07-15T18:10:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.Extensions.dll">
      <PackagePath>System.Text.Encoding.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:01:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.dll">
      <PackagePath>System.Text.Encoding.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encodings.Web.dll">
      <PackagePath>System.Text.Encodings.Web.dll</PackagePath>
      <Modified>2025-07-15T18:10:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Json.dll">
      <PackagePath>System.Text.Json.dll</PackagePath>
      <Modified>2025-07-15T18:10:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.RegularExpressions.dll">
      <PackagePath>System.Text.RegularExpressions.dll</PackagePath>
      <Modified>2025-07-15T18:10:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Channels.dll">
      <PackagePath>System.Threading.Channels.dll</PackagePath>
      <Modified>2025-07-15T18:11:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Overlapped.dll">
      <PackagePath>System.Threading.Overlapped.dll</PackagePath>
      <Modified>2025-07-15T18:00:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Dataflow.dll">
      <PackagePath>System.Threading.Tasks.Dataflow.dll</PackagePath>
      <Modified>2025-07-15T18:11:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Extensions.dll">
      <PackagePath>System.Threading.Tasks.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Parallel.dll">
      <PackagePath>System.Threading.Tasks.Parallel.dll</PackagePath>
      <Modified>2025-07-15T18:11:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.dll">
      <PackagePath>System.Threading.Tasks.dll</PackagePath>
      <Modified>2025-07-15T18:00:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Thread.dll">
      <PackagePath>System.Threading.Thread.dll</PackagePath>
      <Modified>2025-07-15T17:59:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.ThreadPool.dll">
      <PackagePath>System.Threading.ThreadPool.dll</PackagePath>
      <Modified>2025-07-15T17:59:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Timer.dll">
      <PackagePath>System.Threading.Timer.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.dll">
      <PackagePath>System.Threading.dll</PackagePath>
      <Modified>2025-07-15T18:11:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Transactions.Local.dll">
      <PackagePath>System.Transactions.Local.dll</PackagePath>
      <Modified>2025-07-15T18:11:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Transactions.dll">
      <PackagePath>System.Transactions.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ValueTuple.dll">
      <PackagePath>System.ValueTuple.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Web.HttpUtility.dll">
      <PackagePath>System.Web.HttpUtility.dll</PackagePath>
      <Modified>2025-07-15T18:11:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Web.dll">
      <PackagePath>System.Web.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.dll">
      <PackagePath>System.Windows.dll</PackagePath>
      <Modified>2025-07-15T18:01:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.Linq.dll">
      <PackagePath>System.Xml.Linq.dll</PackagePath>
      <Modified>2025-07-15T18:01:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.ReaderWriter.dll">
      <PackagePath>System.Xml.ReaderWriter.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.Serialization.dll">
      <PackagePath>System.Xml.Serialization.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XDocument.dll">
      <PackagePath>System.Xml.XDocument.dll</PackagePath>
      <Modified>2025-07-15T18:01:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XPath.XDocument.dll">
      <PackagePath>System.Xml.XPath.XDocument.dll</PackagePath>
      <Modified>2025-07-15T18:11:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XPath.dll">
      <PackagePath>System.Xml.XPath.dll</PackagePath>
      <Modified>2025-07-15T18:01:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XmlDocument.dll">
      <PackagePath>System.Xml.XmlDocument.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XmlSerializer.dll">
      <PackagePath>System.Xml.XmlSerializer.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.dll">
      <PackagePath>System.Xml.dll</PackagePath>
      <Modified>2025-07-15T18:01:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.dll">
      <PackagePath>System.dll</PackagePath>
      <Modified>2025-07-15T18:01:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\mscorlib.dll">
      <PackagePath>mscorlib.dll</PackagePath>
      <Modified>2025-07-15T18:01:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\netstandard.dll">
      <PackagePath>netstandard.dll</PackagePath>
      <Modified>2025-07-15T18:01:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Accessibility.dll">
      <PackagePath>Accessibility.dll</PackagePath>
      <Modified>2025-07-17T21:41:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\DirectWriteForwarder.dll">
      <PackagePath>DirectWriteForwarder.dll</PackagePath>
      <Modified>2025-07-18T23:05:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.Forms.dll">
      <PackagePath>Microsoft.VisualBasic.Forms.dll</PackagePath>
      <Modified>2025-07-18T23:05:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.dll">
      <PackagePath>Microsoft.VisualBasic.dll</PackagePath>
      <Modified>2025-07-17T21:43:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Registry.AccessControl.dll">
      <PackagePath>Microsoft.Win32.Registry.AccessControl.dll</PackagePath>
      <Modified>2025-07-18T23:05:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.SystemEvents.dll">
      <PackagePath>Microsoft.Win32.SystemEvents.dll</PackagePath>
      <Modified>2025-07-18T23:05:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationCore.dll">
      <PackagePath>PresentationCore.dll</PackagePath>
      <Modified>2025-07-18T23:05:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework-SystemCore.dll">
      <PackagePath>PresentationFramework-SystemCore.dll</PackagePath>
      <Modified>2025-07-18T23:05:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework-SystemData.dll">
      <PackagePath>PresentationFramework-SystemData.dll</PackagePath>
      <Modified>2025-07-18T23:05:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework-SystemDrawing.dll">
      <PackagePath>PresentationFramework-SystemDrawing.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework-SystemXml.dll">
      <PackagePath>PresentationFramework-SystemXml.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework-SystemXmlLinq.dll">
      <PackagePath>PresentationFramework-SystemXmlLinq.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.Aero.dll">
      <PackagePath>PresentationFramework.Aero.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.Aero2.dll">
      <PackagePath>PresentationFramework.Aero2.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.AeroLite.dll">
      <PackagePath>PresentationFramework.AeroLite.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.Classic.dll">
      <PackagePath>PresentationFramework.Classic.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.Luna.dll">
      <PackagePath>PresentationFramework.Luna.dll</PackagePath>
      <Modified>2025-07-18T23:05:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.Royale.dll">
      <PackagePath>PresentationFramework.Royale.dll</PackagePath>
      <Modified>2025-07-18T23:05:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationFramework.dll">
      <PackagePath>PresentationFramework.dll</PackagePath>
      <Modified>2025-07-18T23:05:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\PresentationUI.dll">
      <PackagePath>PresentationUI.dll</PackagePath>
      <Modified>2025-07-18T23:05:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ReachFramework.dll">
      <PackagePath>ReachFramework.dll</PackagePath>
      <Modified>2025-07-18T23:05:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.CodeDom.dll">
      <PackagePath>System.CodeDom.dll</PackagePath>
      <Modified>2025-07-18T23:05:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Configuration.ConfigurationManager.dll">
      <PackagePath>System.Configuration.ConfigurationManager.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Design.dll">
      <PackagePath>System.Design.dll</PackagePath>
      <Modified>2025-07-17T21:43:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.EventLog.Messages.dll">
      <PackagePath>System.Diagnostics.EventLog.Messages.dll</PackagePath>
      <Modified>2025-07-15T17:54:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.EventLog.dll">
      <PackagePath>System.Diagnostics.EventLog.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.PerformanceCounter.dll">
      <PackagePath>System.Diagnostics.PerformanceCounter.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.DirectoryServices.dll">
      <PackagePath>System.DirectoryServices.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.Common.dll">
      <PackagePath>System.Drawing.Common.dll</PackagePath>
      <Modified>2025-07-18T23:05:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.Design.dll">
      <PackagePath>System.Drawing.Design.dll</PackagePath>
      <Modified>2025-07-17T21:43:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.dll">
      <PackagePath>System.Drawing.dll</PackagePath>
      <Modified>2025-07-17T21:43:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Packaging.dll">
      <PackagePath>System.IO.Packaging.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Printing.dll">
      <PackagePath>System.Printing.dll</PackagePath>
      <Modified>2025-07-18T23:05:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Extensions.dll">
      <PackagePath>System.Resources.Extensions.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Pkcs.dll">
      <PackagePath>System.Security.Cryptography.Pkcs.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.ProtectedData.dll">
      <PackagePath>System.Security.Cryptography.ProtectedData.dll</PackagePath>
      <Modified>2025-07-18T23:05:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Xml.dll">
      <PackagePath>System.Security.Cryptography.Xml.dll</PackagePath>
      <Modified>2025-07-18T23:05:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Permissions.dll">
      <PackagePath>System.Security.Permissions.dll</PackagePath>
      <Modified>2025-07-18T23:05:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.AccessControl.dll">
      <PackagePath>System.Threading.AccessControl.dll</PackagePath>
      <Modified>2025-07-18T23:05:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Controls.Ribbon.dll">
      <PackagePath>System.Windows.Controls.Ribbon.dll</PackagePath>
      <Modified>2025-07-18T23:05:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Extensions.dll">
      <PackagePath>System.Windows.Extensions.dll</PackagePath>
      <Modified>2025-07-18T23:05:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Forms.Design.Editors.dll">
      <PackagePath>System.Windows.Forms.Design.Editors.dll</PackagePath>
      <Modified>2025-07-17T21:43:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Forms.Design.dll">
      <PackagePath>System.Windows.Forms.Design.dll</PackagePath>
      <Modified>2025-07-18T23:05:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Forms.Primitives.dll">
      <PackagePath>System.Windows.Forms.Primitives.dll</PackagePath>
      <Modified>2025-07-18T23:05:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Forms.dll">
      <PackagePath>System.Windows.Forms.dll</PackagePath>
      <Modified>2025-07-18T23:05:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Input.Manipulations.dll">
      <PackagePath>System.Windows.Input.Manipulations.dll</PackagePath>
      <Modified>2025-07-18T23:05:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.Presentation.dll">
      <PackagePath>System.Windows.Presentation.dll</PackagePath>
      <Modified>2025-07-18T23:05:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xaml.dll">
      <PackagePath>System.Xaml.dll</PackagePath>
      <Modified>2025-07-18T23:05:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\UIAutomationClient.dll">
      <PackagePath>UIAutomationClient.dll</PackagePath>
      <Modified>2025-07-18T23:05:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\UIAutomationClientSideProviders.dll">
      <PackagePath>UIAutomationClientSideProviders.dll</PackagePath>
      <Modified>2025-07-18T23:05:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\UIAutomationProvider.dll">
      <PackagePath>UIAutomationProvider.dll</PackagePath>
      <Modified>2025-07-18T23:05:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\UIAutomationTypes.dll">
      <PackagePath>UIAutomationTypes.dll</PackagePath>
      <Modified>2025-07-18T23:05:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\WindowsBase.dll">
      <PackagePath>WindowsBase.dll</PackagePath>
      <Modified>2025-07-18T23:05:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\WindowsFormsIntegration.dll">
      <PackagePath>WindowsFormsIntegration.dll</PackagePath>
      <Modified>2025-07-18T23:05:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\Microsoft.DiaSymReader.Native.amd64.dll">
      <PackagePath>Microsoft.DiaSymReader.Native.amd64.dll</PackagePath>
      <Modified>2024-11-27T04:18:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\System.IO.Compression.Native.dll">
      <PackagePath>System.IO.Compression.Native.dll</PackagePath>
      <Modified>2025-07-15T17:57:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clretwrc.dll">
      <PackagePath>clretwrc.dll</PackagePath>
      <Modified>2025-07-15T17:46:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clrgc.dll">
      <PackagePath>clrgc.dll</PackagePath>
      <Modified>2025-07-15T17:43:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clrjit.dll">
      <PackagePath>clrjit.dll</PackagePath>
      <Modified>2025-07-15T17:43:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\coreclr.dll">
      <PackagePath>coreclr.dll</PackagePath>
      <Modified>2025-07-15T17:40:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\createdump.exe">
      <PackagePath>createdump.exe</PackagePath>
      <Modified>2025-07-15T17:46:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\hostfxr.dll">
      <PackagePath>hostfxr.dll</PackagePath>
      <Modified>2025-07-15T17:56:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\hostpolicy.dll">
      <PackagePath>hostpolicy.dll</PackagePath>
      <Modified>2025-07-15T17:56:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordaccore.dll">
      <PackagePath>mscordaccore.dll</PackagePath>
      <Modified>2025-07-15T17:48:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordaccore_amd64_amd64_8.0.1925.36514.dll">
      <PackagePath>mscordaccore_amd64_amd64_8.0.1925.36514.dll</PackagePath>
      <Modified>2025-07-15T17:48:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordbi.dll">
      <PackagePath>mscordbi.dll</PackagePath>
      <Modified>2025-07-15T17:49:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscorrc.dll">
      <PackagePath>mscorrc.dll</PackagePath>
      <Modified>2025-07-15T17:46:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\msquic.dll">
      <PackagePath>msquic.dll</PackagePath>
      <Modified>2025-03-03T22:23:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\D3DCompiler_47_cor3.dll">
      <PackagePath>D3DCompiler_47_cor3.dll</PackagePath>
      <Modified>2024-02-21T12:59:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\PenImc_cor3.dll">
      <PackagePath>PenImc_cor3.dll</PackagePath>
      <Modified>2025-07-18T21:58:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\PresentationNative_cor3.dll">
      <PackagePath>PresentationNative_cor3.dll</PackagePath>
      <Modified>2025-03-19T06:51:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\vcruntime140_cor3.dll">
      <PackagePath>vcruntime140_cor3.dll</PackagePath>
      <Modified>2025-05-20T01:31:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\wpfgfx_cor3.dll">
      <PackagePath>wpfgfx_cor3.dll</PackagePath>
      <Modified>2025-07-18T21:58:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>cs\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\PresentationCore.resources.dll">
      <PackagePath>cs\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\PresentationFramework.resources.dll">
      <PackagePath>cs\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\PresentationUI.resources.dll">
      <PackagePath>cs\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\ReachFramework.resources.dll">
      <PackagePath>cs\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>cs\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\System.Windows.Forms.Design.resources.dll">
      <PackagePath>cs\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>cs\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\System.Windows.Forms.resources.dll">
      <PackagePath>cs\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>cs\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\System.Xaml.resources.dll">
      <PackagePath>cs\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\UIAutomationClient.resources.dll">
      <PackagePath>cs\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>cs\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\UIAutomationProvider.resources.dll">
      <PackagePath>cs\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\UIAutomationTypes.resources.dll">
      <PackagePath>cs\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\WindowsBase.resources.dll">
      <PackagePath>cs\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\cs\WindowsFormsIntegration.resources.dll">
      <PackagePath>cs\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>de\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\PresentationCore.resources.dll">
      <PackagePath>de\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\PresentationFramework.resources.dll">
      <PackagePath>de\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\PresentationUI.resources.dll">
      <PackagePath>de\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\ReachFramework.resources.dll">
      <PackagePath>de\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>de\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\System.Windows.Forms.Design.resources.dll">
      <PackagePath>de\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>de\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\System.Windows.Forms.resources.dll">
      <PackagePath>de\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>de\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\System.Xaml.resources.dll">
      <PackagePath>de\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\UIAutomationClient.resources.dll">
      <PackagePath>de\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>de\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\UIAutomationProvider.resources.dll">
      <PackagePath>de\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\UIAutomationTypes.resources.dll">
      <PackagePath>de\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\WindowsBase.resources.dll">
      <PackagePath>de\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\de\WindowsFormsIntegration.resources.dll">
      <PackagePath>de\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>es\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\PresentationCore.resources.dll">
      <PackagePath>es\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\PresentationFramework.resources.dll">
      <PackagePath>es\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\PresentationUI.resources.dll">
      <PackagePath>es\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\ReachFramework.resources.dll">
      <PackagePath>es\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>es\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\System.Windows.Forms.Design.resources.dll">
      <PackagePath>es\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>es\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\System.Windows.Forms.resources.dll">
      <PackagePath>es\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>es\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\System.Xaml.resources.dll">
      <PackagePath>es\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\UIAutomationClient.resources.dll">
      <PackagePath>es\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>es\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\UIAutomationProvider.resources.dll">
      <PackagePath>es\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\UIAutomationTypes.resources.dll">
      <PackagePath>es\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\WindowsBase.resources.dll">
      <PackagePath>es\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\es\WindowsFormsIntegration.resources.dll">
      <PackagePath>es\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>fr\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\PresentationCore.resources.dll">
      <PackagePath>fr\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\PresentationFramework.resources.dll">
      <PackagePath>fr\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\PresentationUI.resources.dll">
      <PackagePath>fr\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\ReachFramework.resources.dll">
      <PackagePath>fr\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>fr\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\System.Windows.Forms.Design.resources.dll">
      <PackagePath>fr\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>fr\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\System.Windows.Forms.resources.dll">
      <PackagePath>fr\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>fr\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\System.Xaml.resources.dll">
      <PackagePath>fr\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\UIAutomationClient.resources.dll">
      <PackagePath>fr\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>fr\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\UIAutomationProvider.resources.dll">
      <PackagePath>fr\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\UIAutomationTypes.resources.dll">
      <PackagePath>fr\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\WindowsBase.resources.dll">
      <PackagePath>fr\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\fr\WindowsFormsIntegration.resources.dll">
      <PackagePath>fr\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>it\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\PresentationCore.resources.dll">
      <PackagePath>it\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\PresentationFramework.resources.dll">
      <PackagePath>it\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\PresentationUI.resources.dll">
      <PackagePath>it\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\ReachFramework.resources.dll">
      <PackagePath>it\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>it\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\System.Windows.Forms.Design.resources.dll">
      <PackagePath>it\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>it\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\System.Windows.Forms.resources.dll">
      <PackagePath>it\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>it\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\System.Xaml.resources.dll">
      <PackagePath>it\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\UIAutomationClient.resources.dll">
      <PackagePath>it\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>it\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\UIAutomationProvider.resources.dll">
      <PackagePath>it\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\UIAutomationTypes.resources.dll">
      <PackagePath>it\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\WindowsBase.resources.dll">
      <PackagePath>it\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\it\WindowsFormsIntegration.resources.dll">
      <PackagePath>it\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>ja\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\PresentationCore.resources.dll">
      <PackagePath>ja\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\PresentationFramework.resources.dll">
      <PackagePath>ja\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\PresentationUI.resources.dll">
      <PackagePath>ja\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\ReachFramework.resources.dll">
      <PackagePath>ja\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>ja\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\System.Windows.Forms.Design.resources.dll">
      <PackagePath>ja\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>ja\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\System.Windows.Forms.resources.dll">
      <PackagePath>ja\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>ja\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\System.Xaml.resources.dll">
      <PackagePath>ja\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\UIAutomationClient.resources.dll">
      <PackagePath>ja\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>ja\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\UIAutomationProvider.resources.dll">
      <PackagePath>ja\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\UIAutomationTypes.resources.dll">
      <PackagePath>ja\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\WindowsBase.resources.dll">
      <PackagePath>ja\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ja\WindowsFormsIntegration.resources.dll">
      <PackagePath>ja\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>ko\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\PresentationCore.resources.dll">
      <PackagePath>ko\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\PresentationFramework.resources.dll">
      <PackagePath>ko\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\PresentationUI.resources.dll">
      <PackagePath>ko\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\ReachFramework.resources.dll">
      <PackagePath>ko\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>ko\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\System.Windows.Forms.Design.resources.dll">
      <PackagePath>ko\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>ko\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\System.Windows.Forms.resources.dll">
      <PackagePath>ko\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>ko\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\System.Xaml.resources.dll">
      <PackagePath>ko\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\UIAutomationClient.resources.dll">
      <PackagePath>ko\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>ko\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\UIAutomationProvider.resources.dll">
      <PackagePath>ko\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\UIAutomationTypes.resources.dll">
      <PackagePath>ko\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\WindowsBase.resources.dll">
      <PackagePath>ko\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ko\WindowsFormsIntegration.resources.dll">
      <PackagePath>ko\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>pl\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\PresentationCore.resources.dll">
      <PackagePath>pl\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\PresentationFramework.resources.dll">
      <PackagePath>pl\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\PresentationUI.resources.dll">
      <PackagePath>pl\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\ReachFramework.resources.dll">
      <PackagePath>pl\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>pl\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\System.Windows.Forms.Design.resources.dll">
      <PackagePath>pl\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>pl\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\System.Windows.Forms.resources.dll">
      <PackagePath>pl\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>pl\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\System.Xaml.resources.dll">
      <PackagePath>pl\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\UIAutomationClient.resources.dll">
      <PackagePath>pl\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>pl\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\UIAutomationProvider.resources.dll">
      <PackagePath>pl\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\UIAutomationTypes.resources.dll">
      <PackagePath>pl\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\WindowsBase.resources.dll">
      <PackagePath>pl\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pl\WindowsFormsIntegration.resources.dll">
      <PackagePath>pl\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>pt-BR\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\PresentationCore.resources.dll">
      <PackagePath>pt-BR\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\PresentationFramework.resources.dll">
      <PackagePath>pt-BR\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\PresentationUI.resources.dll">
      <PackagePath>pt-BR\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\ReachFramework.resources.dll">
      <PackagePath>pt-BR\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>pt-BR\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\System.Windows.Forms.Design.resources.dll">
      <PackagePath>pt-BR\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>pt-BR\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\System.Windows.Forms.resources.dll">
      <PackagePath>pt-BR\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>pt-BR\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\System.Xaml.resources.dll">
      <PackagePath>pt-BR\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\UIAutomationClient.resources.dll">
      <PackagePath>pt-BR\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>pt-BR\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\UIAutomationProvider.resources.dll">
      <PackagePath>pt-BR\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\UIAutomationTypes.resources.dll">
      <PackagePath>pt-BR\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\WindowsBase.resources.dll">
      <PackagePath>pt-BR\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\pt-BR\WindowsFormsIntegration.resources.dll">
      <PackagePath>pt-BR\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>ru\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\PresentationCore.resources.dll">
      <PackagePath>ru\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\PresentationFramework.resources.dll">
      <PackagePath>ru\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\PresentationUI.resources.dll">
      <PackagePath>ru\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\ReachFramework.resources.dll">
      <PackagePath>ru\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>ru\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\System.Windows.Forms.Design.resources.dll">
      <PackagePath>ru\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>ru\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\System.Windows.Forms.resources.dll">
      <PackagePath>ru\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>ru\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\System.Xaml.resources.dll">
      <PackagePath>ru\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\UIAutomationClient.resources.dll">
      <PackagePath>ru\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>ru\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\UIAutomationProvider.resources.dll">
      <PackagePath>ru\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\UIAutomationTypes.resources.dll">
      <PackagePath>ru\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\WindowsBase.resources.dll">
      <PackagePath>ru\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\ru\WindowsFormsIntegration.resources.dll">
      <PackagePath>ru\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>tr\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\PresentationCore.resources.dll">
      <PackagePath>tr\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\PresentationFramework.resources.dll">
      <PackagePath>tr\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\PresentationUI.resources.dll">
      <PackagePath>tr\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\ReachFramework.resources.dll">
      <PackagePath>tr\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>tr\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\System.Windows.Forms.Design.resources.dll">
      <PackagePath>tr\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>tr\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\System.Windows.Forms.resources.dll">
      <PackagePath>tr\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>tr\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\System.Xaml.resources.dll">
      <PackagePath>tr\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\UIAutomationClient.resources.dll">
      <PackagePath>tr\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>tr\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\UIAutomationProvider.resources.dll">
      <PackagePath>tr\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\UIAutomationTypes.resources.dll">
      <PackagePath>tr\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\WindowsBase.resources.dll">
      <PackagePath>tr\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\tr\WindowsFormsIntegration.resources.dll">
      <PackagePath>tr\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>zh-Hans\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\PresentationCore.resources.dll">
      <PackagePath>zh-Hans\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\PresentationFramework.resources.dll">
      <PackagePath>zh-Hans\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\PresentationUI.resources.dll">
      <PackagePath>zh-Hans\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\ReachFramework.resources.dll">
      <PackagePath>zh-Hans\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>zh-Hans\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\System.Windows.Forms.Design.resources.dll">
      <PackagePath>zh-Hans\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>zh-Hans\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\System.Windows.Forms.resources.dll">
      <PackagePath>zh-Hans\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>zh-Hans\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\System.Xaml.resources.dll">
      <PackagePath>zh-Hans\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\UIAutomationClient.resources.dll">
      <PackagePath>zh-Hans\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>zh-Hans\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\UIAutomationProvider.resources.dll">
      <PackagePath>zh-Hans\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\UIAutomationTypes.resources.dll">
      <PackagePath>zh-Hans\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\WindowsBase.resources.dll">
      <PackagePath>zh-Hans\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hans\WindowsFormsIntegration.resources.dll">
      <PackagePath>zh-Hans\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\Microsoft.VisualBasic.Forms.resources.dll">
      <PackagePath>zh-Hant\Microsoft.VisualBasic.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\PresentationCore.resources.dll">
      <PackagePath>zh-Hant\PresentationCore.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\PresentationFramework.resources.dll">
      <PackagePath>zh-Hant\PresentationFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\PresentationUI.resources.dll">
      <PackagePath>zh-Hant\PresentationUI.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\ReachFramework.resources.dll">
      <PackagePath>zh-Hant\ReachFramework.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\System.Windows.Controls.Ribbon.resources.dll">
      <PackagePath>zh-Hant\System.Windows.Controls.Ribbon.resources.dll</PackagePath>
      <Modified>2025-07-18T22:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\System.Windows.Forms.Design.resources.dll">
      <PackagePath>zh-Hant\System.Windows.Forms.Design.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\System.Windows.Forms.Primitives.resources.dll">
      <PackagePath>zh-Hant\System.Windows.Forms.Primitives.resources.dll</PackagePath>
      <Modified>2025-07-17T21:42:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\System.Windows.Forms.resources.dll">
      <PackagePath>zh-Hant\System.Windows.Forms.resources.dll</PackagePath>
      <Modified>2025-07-17T21:43:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\System.Windows.Input.Manipulations.resources.dll">
      <PackagePath>zh-Hant\System.Windows.Input.Manipulations.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\System.Xaml.resources.dll">
      <PackagePath>zh-Hant\System.Xaml.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\UIAutomationClient.resources.dll">
      <PackagePath>zh-Hant\UIAutomationClient.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\UIAutomationClientSideProviders.resources.dll">
      <PackagePath>zh-Hant\UIAutomationClientSideProviders.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\UIAutomationProvider.resources.dll">
      <PackagePath>zh-Hant\UIAutomationProvider.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\UIAutomationTypes.resources.dll">
      <PackagePath>zh-Hant\UIAutomationTypes.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\WindowsBase.resources.dll">
      <PackagePath>zh-Hant\WindowsBase.resources.dll</PackagePath>
      <Modified>2025-07-18T21:58:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\zh-Hant\WindowsFormsIntegration.resources.dll">
      <PackagePath>zh-Hant\WindowsFormsIntegration.resources.dll</PackagePath>
      <Modified>2025-07-18T21:59:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
      <Modified>2024-11-11T17:23:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
      <Modified>2024-11-11T17:23:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.2.2\lib\net6.0\CommunityToolkit.Mvvm.dll">
      <PackagePath>CommunityToolkit.Mvvm.dll</PackagePath>
      <Modified>2023-10-24T20:16:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Configuration.dll</PackagePath>
      <Modified>2023-10-31T14:59:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Abstractions.dll</PackagePath>
      <Modified>2023-10-31T14:59:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Binder.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Binder.dll</PackagePath>
      <Modified>2023-10-31T14:59:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.commandline\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll">
      <PackagePath>Microsoft.Extensions.Configuration.CommandLine.dll</PackagePath>
      <Modified>2023-10-31T14:59:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.environmentvariables\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll">
      <PackagePath>Microsoft.Extensions.Configuration.EnvironmentVariables.dll</PackagePath>
      <Modified>2023-10-31T14:59:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.fileextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.FileExtensions.dll</PackagePath>
      <Modified>2023-10-31T14:59:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.json\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Json.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Json.dll</PackagePath>
      <Modified>2023-10-31T14:59:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.usersecrets\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll">
      <PackagePath>Microsoft.Extensions.Configuration.UserSecrets.dll</PackagePath>
      <Modified>2023-10-31T14:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.dll</PackagePath>
      <Modified>2023-10-31T14:59:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.Abstractions.dll</PackagePath>
      <Modified>2023-10-31T14:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics\8.0.0\lib\net8.0\Microsoft.Extensions.Diagnostics.dll">
      <PackagePath>Microsoft.Extensions.Diagnostics.dll</PackagePath>
      <Modified>2023-10-31T14:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Diagnostics.Abstractions.dll</PackagePath>
      <Modified>2023-10-31T14:59:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Abstractions.dll</PackagePath>
      <Modified>2023-10-31T14:59:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.physical\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Physical.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Physical.dll</PackagePath>
      <Modified>2023-10-31T14:59:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.filesystemglobbing\8.0.0\lib\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll">
      <PackagePath>Microsoft.Extensions.FileSystemGlobbing.dll</PackagePath>
      <Modified>2023-10-31T14:58:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting\8.0.0\lib\net8.0\Microsoft.Extensions.Hosting.dll">
      <PackagePath>Microsoft.Extensions.Hosting.dll</PackagePath>
      <Modified>2023-10-31T14:59:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Hosting.Abstractions.dll</PackagePath>
      <Modified>2023-10-31T14:59:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.dll">
      <PackagePath>Microsoft.Extensions.Logging.dll</PackagePath>
      <Modified>2023-10-31T14:59:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Logging.Abstractions.dll</PackagePath>
      <Modified>2023-10-31T14:59:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Logging.Configuration.dll</PackagePath>
      <Modified>2023-10-31T14:59:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.console\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Console.dll">
      <PackagePath>Microsoft.Extensions.Logging.Console.dll</PackagePath>
      <Modified>2023-10-31T14:59:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Debug.dll">
      <PackagePath>Microsoft.Extensions.Logging.Debug.dll</PackagePath>
      <Modified>2023-10-31T14:59:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.eventlog\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.EventLog.dll">
      <PackagePath>Microsoft.Extensions.Logging.EventLog.dll</PackagePath>
      <Modified>2023-10-31T14:59:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.eventsource\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.EventSource.dll">
      <PackagePath>Microsoft.Extensions.Logging.EventSource.dll</PackagePath>
      <Modified>2023-10-31T14:59:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\lib\net8.0\Microsoft.Extensions.Options.dll">
      <PackagePath>Microsoft.Extensions.Options.dll</PackagePath>
      <Modified>2023-10-31T14:59:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options.configurationextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll">
      <PackagePath>Microsoft.Extensions.Options.ConfigurationExtensions.dll</PackagePath>
      <Modified>2023-10-31T14:59:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\8.0.0\lib\net8.0\Microsoft.Extensions.Primitives.dll">
      <PackagePath>Microsoft.Extensions.Primitives.dll</PackagePath>
      <Modified>2023-10-31T14:58:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
      <Modified>2023-10-06T00:04:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
      <Modified>2023-10-06T14:59:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Widgets.Providers.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Providers.Projection.dll</PackagePath>
      <Modified>2023-07-24T22:17:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\lib\net6.0-windows10.0.18362.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
      <Modified>2023-10-06T23:44:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio\2.2.1\lib\net6.0-windows7.0\NAudio.dll">
      <PackagePath>NAudio.dll</PackagePath>
      <Modified>2023-09-04T19:49:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.asio\2.2.1\lib\netstandard2.0\NAudio.Asio.dll">
      <PackagePath>NAudio.Asio.dll</PackagePath>
      <Modified>2023-09-04T19:49:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.core\2.2.1\lib\netstandard2.0\NAudio.Core.dll">
      <PackagePath>NAudio.Core.dll</PackagePath>
      <Modified>2023-09-04T19:49:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.midi\2.2.1\lib\netstandard2.0\NAudio.Midi.dll">
      <PackagePath>NAudio.Midi.dll</PackagePath>
      <Modified>2023-09-04T19:49:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.wasapi\2.2.1\lib\netstandard2.0\NAudio.Wasapi.dll">
      <PackagePath>NAudio.Wasapi.dll</PackagePath>
      <Modified>2023-09-04T19:49:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.winforms\2.2.1\lib\netcoreapp3.1\NAudio.WinForms.dll">
      <PackagePath>NAudio.WinForms.dll</PackagePath>
      <Modified>2023-09-04T19:49:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.winmm\2.2.1\lib\netstandard2.0\NAudio.WinMM.dll">
      <PackagePath>NAudio.WinMM.dll</PackagePath>
      <Modified>2023-09-04T19:49:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\LockScreenLogo.scale-200.png">
      <PackagePath>Assets\LockScreenLogo.scale-200.png</PackagePath>
      <Modified>2025-08-22T14:32:47.694</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\SplashScreen.scale-200.png">
      <PackagePath>Assets\SplashScreen.scale-200.png</PackagePath>
      <Modified>2025-08-22T14:32:47.695</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Square150x150Logo.scale-200.png">
      <PackagePath>Assets\Square150x150Logo.scale-200.png</PackagePath>
      <Modified>2025-08-22T14:32:47.696</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Square44x44Logo.scale-200.png">
      <PackagePath>Assets\Square44x44Logo.scale-200.png</PackagePath>
      <Modified>2025-08-22T14:32:47.697</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <PackagePath>Assets\Square44x44Logo.targetsize-24_altform-unplated.png</PackagePath>
      <Modified>2025-08-22T14:32:47.699</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\StoreLogo.png">
      <PackagePath>Assets\StoreLogo.png</PackagePath>
      <Modified>2025-08-22T14:32:47.699</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Wide310x150Logo.scale-200.png">
      <PackagePath>Assets\Wide310x150Logo.scale-200.png</PackagePath>
      <Modified>2025-08-22T14:32:47.700</Modified>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.4.msix">
      <Name>Microsoft.WindowsAppRuntime.1.4</Name>
      <Version>4000.1010.1349.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.4, MinVersion = 4000.1010.1349.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.4.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.4.msix">
      <Name>Microsoft.WindowsAppRuntime.1.4</Name>
      <Version>4000.1010.1349.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.4, MinVersion = 4000.1010.1349.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.4.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.4.msix">
      <Name>Microsoft.WindowsAppRuntime.1.4</Name>
      <Version>4000.1010.1349.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.4, MinVersion = 4000.1010.1349.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.4.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.4.msix">
      <Name>Microsoft.WindowsAppRuntime.1.4</Name>
      <Version>4000.1010.1349.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.4, MinVersion = 4000.1010.1349.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.4.231008000\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.4.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>