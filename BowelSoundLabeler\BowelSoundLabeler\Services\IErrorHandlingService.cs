namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 错误处理服务接口
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// 处理未捕获的异常
        /// </summary>
        Task HandleUnhandledExceptionAsync(Exception exception, string context = "");

        /// <summary>
        /// 记录操作日志
        /// </summary>
        Task LogOperationAsync(string operation, string details = "", bool isSuccess = true);

        /// <summary>
        /// 记录用户操作
        /// </summary>
        Task LogUserActionAsync(string action, string details = "");

        /// <summary>
        /// 获取错误报告
        /// </summary>
        Task<string> GenerateErrorReportAsync(Exception exception);

        /// <summary>
        /// 清理旧日志文件
        /// </summary>
        Task CleanupOldLogsAsync(int daysToKeep = 30);
    }
}
