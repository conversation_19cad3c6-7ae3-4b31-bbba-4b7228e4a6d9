using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 音频处理服务接口
    /// </summary>
    public interface IAudioProcessingService
    {
        /// <summary>
        /// 验证音频文件格式
        /// </summary>
        Task<bool> ValidateAudioFormatAsync(AudioFile audioFile);

        /// <summary>
        /// 获取音频文件信息
        /// </summary>
        Task<AudioFileInfo?> GetAudioInfoAsync(AudioFile audioFile);

        /// <summary>
        /// 处理单个文件标注
        /// </summary>
        Task<bool> ProcessFileLabelingAsync(FileLabelingInfo labelingInfo, bool overwrite = false);

        /// <summary>
        /// 批量处理文件标注
        /// </summary>
        Task<BatchProcessingResult> ProcessBatchLabelingAsync(
            IList<FileLabelingInfo> labelingInfos, 
            IProgress<BatchProcessingProgress>? progress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查文件是否可以安全重命名
        /// </summary>
        Task<bool> CanRenameFileAsync(string oldPath, string newPath);
    }

    /// <summary>
    /// 音频文件信息
    /// </summary>
    public class AudioFileInfo
    {
        public TimeSpan Duration { get; set; }
        public int SampleRate { get; set; }
        public int Channels { get; set; }
        public int BitsPerSample { get; set; }
        public string Format { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 批量处理结果
    /// </summary>
    public class BatchProcessingResult
    {
        public int TotalFiles { get; set; }
        public int ProcessedFiles { get; set; }
        public int SkippedFiles { get; set; }
        public int ErrorFiles { get; set; }
        public List<string> Errors { get; set; } = new();
        public bool IsCompleted { get; set; }
        public bool IsCancelled { get; set; }
    }

    /// <summary>
    /// 批量处理进度
    /// </summary>
    public class BatchProcessingProgress
    {
        public int CurrentIndex { get; set; }
        public int TotalCount { get; set; }
        public string CurrentFileName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public double ProgressPercentage => TotalCount > 0 ? (double)CurrentIndex / TotalCount * 100 : 0;
    }
}
