using System.Text;
using Microsoft.Extensions.Logging;
using Windows.Storage;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 错误处理服务实现
    /// </summary>
    public class ErrorHandlingService : IErrorHandlingService
    {
        private readonly ILogger<ErrorHandlingService> _logger;
        private readonly IDialogService _dialogService;
        private readonly string _logFolderPath;

        public ErrorHandlingService(ILogger<ErrorHandlingService> logger, IDialogService dialogService)
        {
            _logger = logger;
            _dialogService = dialogService;
            _logFolderPath = Path.Combine(ApplicationData.Current.LocalFolder.Path, "Logs");
            
            // 确保日志文件夹存在
            Directory.CreateDirectory(_logFolderPath);
        }

        public async Task HandleUnhandledExceptionAsync(Exception exception, string context = "")
        {
            try
            {
                _logger.LogCritical(exception, "未处理的异常发生在: {Context}", context);
                
                // 生成错误报告
                var errorReport = await GenerateErrorReportAsync(exception);
                
                // 保存错误报告到文件
                var errorFileName = $"error_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                var errorFilePath = Path.Combine(_logFolderPath, errorFileName);
                await File.WriteAllTextAsync(errorFilePath, errorReport, Encoding.UTF8);
                
                // 显示用户友好的错误消息
                var userMessage = GetUserFriendlyErrorMessage(exception);
                await _dialogService.ShowErrorAsync("应用程序错误", 
                    $"{userMessage}\n\n错误详情已保存到日志文件中。\n如果问题持续存在，请联系技术支持。");
            }
            catch (Exception ex)
            {
                // 如果错误处理本身出错，至少记录到系统日志
                _logger.LogCritical(ex, "错误处理服务本身发生异常");
            }
        }

        public async Task LogOperationAsync(string operation, string details = "", bool isSuccess = true)
        {
            try
            {
                var logLevel = isSuccess ? LogLevel.Information : LogLevel.Warning;
                _logger.Log(logLevel, "操作: {Operation}, 详情: {Details}, 成功: {IsSuccess}", 
                    operation, details, isSuccess);
                
                // 写入操作日志文件
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [{(isSuccess ? "SUCCESS" : "FAILED")}] {operation}";
                if (!string.IsNullOrEmpty(details))
                {
                    logEntry += $" - {details}";
                }
                logEntry += Environment.NewLine;
                
                var logFileName = $"operations_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(_logFolderPath, logFileName);
                await File.AppendAllTextAsync(logFilePath, logEntry, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录操作日志时发生错误");
            }
        }

        public async Task LogUserActionAsync(string action, string details = "")
        {
            try
            {
                _logger.LogInformation("用户操作: {Action}, 详情: {Details}", action, details);
                
                // 写入用户操作日志文件
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [USER] {action}";
                if (!string.IsNullOrEmpty(details))
                {
                    logEntry += $" - {details}";
                }
                logEntry += Environment.NewLine;
                
                var logFileName = $"user_actions_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(_logFolderPath, logFileName);
                await File.AppendAllTextAsync(logFilePath, logEntry, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录用户操作日志时发生错误");
            }
        }

        public async Task<string> GenerateErrorReportAsync(Exception exception)
        {
            try
            {
                var report = new StringBuilder();
                report.AppendLine("=== 错误报告 ===");
                report.AppendLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"应用程序: BowelSoundLabeler");
                report.AppendLine($"版本: 1.0.0");
                report.AppendLine();
                
                report.AppendLine("=== 异常信息 ===");
                var currentException = exception;
                int level = 0;
                
                while (currentException != null)
                {
                    var indent = new string(' ', level * 2);
                    report.AppendLine($"{indent}异常类型: {currentException.GetType().FullName}");
                    report.AppendLine($"{indent}异常消息: {currentException.Message}");
                    report.AppendLine($"{indent}堆栈跟踪:");
                    report.AppendLine(currentException.StackTrace);
                    report.AppendLine();
                    
                    currentException = currentException.InnerException;
                    level++;
                }
                
                report.AppendLine("=== 系统信息 ===");
                report.AppendLine($"操作系统: {Environment.OSVersion}");
                report.AppendLine($"CLR版本: {Environment.Version}");
                report.AppendLine($"工作目录: {Environment.CurrentDirectory}");
                report.AppendLine($"机器名: {Environment.MachineName}");
                report.AppendLine($"用户名: {Environment.UserName}");
                
                return report.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成错误报告时发生错误");
                return $"生成错误报告失败: {ex.Message}\n\n原始异常: {exception}";
            }
        }

        public async Task CleanupOldLogsAsync(int daysToKeep = 30)
        {
            try
            {
                if (!Directory.Exists(_logFolderPath))
                    return;
                
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var logFiles = Directory.GetFiles(_logFolderPath, "*.log")
                    .Concat(Directory.GetFiles(_logFolderPath, "*.txt"))
                    .Where(file => File.GetCreationTime(file) < cutoffDate);
                
                int deletedCount = 0;
                foreach (var file in logFiles)
                {
                    try
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除日志文件失败: {FilePath}", file);
                    }
                }
                
                if (deletedCount > 0)
                {
                    _logger.LogInformation("清理了 {Count} 个旧日志文件", deletedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理旧日志文件时发生错误");
            }
        }

        private string GetUserFriendlyErrorMessage(Exception exception)
        {
            return exception switch
            {
                FileNotFoundException => "找不到指定的文件。请检查文件是否存在。",
                DirectoryNotFoundException => "找不到指定的文件夹。请检查路径是否正确。",
                UnauthorizedAccessException => "没有权限访问文件或文件夹。请检查文件权限。",
                IOException => "文件操作失败。文件可能正在被其他程序使用。",
                ArgumentException => "输入参数无效。请检查输入的数据。",
                InvalidOperationException => "当前操作无效。请检查操作条件。",
                OutOfMemoryException => "内存不足。请关闭其他程序后重试。",
                _ => "应用程序遇到了一个意外错误。"
            };
        }
    }
}
