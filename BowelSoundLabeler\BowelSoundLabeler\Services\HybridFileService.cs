using Microsoft.Extensions.Logging;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 混合文件服务：优先使用Win32 API，失败时回退到WinUI API
    /// </summary>
    public class HybridFileService : IFileService
    {
        private readonly ILogger<HybridFileService> _logger;
        private readonly Win32FileService _win32FileService;
        private readonly FileService _winuiFileService;

        public HybridFileService(ILogger<HybridFileService> logger)
        {
            _logger = logger;

            // 创建特定类型的logger
            var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
            var win32Logger = loggerFactory.CreateLogger<Win32FileService>();
            var winuiLogger = loggerFactory.CreateLogger<FileService>();

            _win32FileService = new Win32FileService(win32Logger);
            _winuiFileService = new FileService(winuiLogger);
        }

        public async Task<AudioFile?> SelectSingleFileAsync(string? initialFolder = null)
        {
            try
            {
                // 首先尝试使用Win32 API（支持设置初始文件夹）
                _logger.LogInformation("尝试使用Win32 API选择单个文件，初始文件夹: {Folder}", initialFolder ?? "无");
                var result = await _win32FileService.SelectSingleFileAsync(initialFolder);
                
                if (result != null)
                {
                    _logger.LogInformation("Win32 API成功选择文件: {FilePath}", result.FilePath);
                    return result;
                }
                
                _logger.LogWarning("Win32 API未选择文件，回退到WinUI API");
                // 如果Win32 API失败，回退到WinUI API
                return await _winuiFileService.SelectSingleFileAsync(initialFolder);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Win32 API选择文件失败，回退到WinUI API");
                try
                {
                    return await _winuiFileService.SelectSingleFileAsync(initialFolder);
                }
                catch (Exception winuiEx)
                {
                    _logger.LogError(winuiEx, "WinUI API也失败了");
                    throw;
                }
            }
        }

        public async Task<IList<AudioFile>> SelectMultipleFilesAsync(string? initialFolder = null)
        {
            try
            {
                // 首先尝试使用Win32 API（支持设置初始文件夹）
                _logger.LogInformation("尝试使用Win32 API选择多个文件，初始文件夹: {Folder}", initialFolder ?? "无");
                var result = await _win32FileService.SelectMultipleFilesAsync(initialFolder);
                
                if (result.Count > 0)
                {
                    _logger.LogInformation("Win32 API成功选择 {Count} 个文件", result.Count);
                    return result;
                }
                
                _logger.LogWarning("Win32 API未选择文件，回退到WinUI API");
                // 如果Win32 API失败，回退到WinUI API
                return await _winuiFileService.SelectMultipleFilesAsync(initialFolder);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Win32 API选择文件失败，回退到WinUI API");
                try
                {
                    return await _winuiFileService.SelectMultipleFilesAsync(initialFolder);
                }
                catch (Exception winuiEx)
                {
                    _logger.LogError(winuiEx, "WinUI API也失败了");
                    throw;
                }
            }
        }

        public async Task<bool> ValidateAudioFileAsync(AudioFile audioFile)
        {
            // 使用WinUI服务的验证逻辑
            return await _winuiFileService.ValidateAudioFileAsync(audioFile);
        }

        public async Task<bool> RenameFileAsync(string oldPath, string newPath, bool overwrite = false)
        {
            // 使用WinUI服务的重命名逻辑
            return await _winuiFileService.RenameFileAsync(oldPath, newPath, overwrite);
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            // 使用WinUI服务的文件存在检查逻辑
            return await _winuiFileService.FileExistsAsync(filePath);
        }

        public IReadOnlyList<string> GetSupportedExtensions()
        {
            // 使用WinUI服务的支持扩展名列表
            return _winuiFileService.GetSupportedExtensions();
        }
    }
}
