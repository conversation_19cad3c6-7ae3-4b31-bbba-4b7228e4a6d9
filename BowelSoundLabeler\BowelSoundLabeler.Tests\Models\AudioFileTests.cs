using BowelSoundLabeler.Models;
using Xunit;

namespace BowelSoundLabeler.Tests.Models
{
    public class AudioFileTests
    {
        [Fact]
        public void FromPath_ValidCsvFile_ShouldCreateValidAudioFile()
        {
            // Arrange
            var tempFile = Path.GetTempFileName();
            File.WriteAllText(tempFile, "header1,header2\nvalue1,value2");
            var csvPath = Path.ChangeExtension(tempFile, ".csv");
            File.Move(tempFile, csvPath);

            try
            {
                // Act
                var audioFile = AudioFile.FromPath(csvPath);

                // Assert
                Assert.NotNull(audioFile);
                Assert.Equal(csvPath, audioFile.FilePath);
                Assert.Equal(Path.GetFileNameWithoutExtension(csvPath), audioFile.FileName);
                Assert.Equal(".csv", audioFile.FileExtension);
                Assert.True(audioFile.IsValid);
                Assert.True(audioFile.FileSize > 0);
            }
            finally
            {
                // Cleanup
                if (File.Exists(csvPath))
                    File.Delete(csvPath);
            }
        }

        [Fact]
        public void FromPath_UnsupportedFileType_ShouldCreateInvalidAudioFile()
        {
            // Arrange
            var tempFile = Path.GetTempFileName();
            var txtPath = Path.ChangeExtension(tempFile, ".txt");
            File.Move(tempFile, txtPath);

            try
            {
                // Act
                var audioFile = AudioFile.FromPath(txtPath);

                // Assert
                Assert.NotNull(audioFile);
                Assert.Equal(txtPath, audioFile.FilePath);
                Assert.Equal(".txt", audioFile.FileExtension);
                Assert.False(audioFile.IsValid);
            }
            finally
            {
                // Cleanup
                if (File.Exists(txtPath))
                    File.Delete(txtPath);
            }
        }

        [Theory]
        [InlineData(".csv", true)]
        [InlineData(".wav", true)]
        [InlineData(".mp3", true)]
        [InlineData(".m4a", true)]
        [InlineData(".flac", true)]
        [InlineData(".mat", true)]
        [InlineData(".txt", false)]
        [InlineData(".doc", false)]
        public void FromPath_VariousExtensions_ShouldValidateCorrectly(string extension, bool expectedValid)
        {
            // Arrange
            var tempFile = Path.GetTempFileName();
            var testPath = Path.ChangeExtension(tempFile, extension);
            File.Move(tempFile, testPath);

            try
            {
                // Act
                var audioFile = AudioFile.FromPath(testPath);

                // Assert
                Assert.Equal(expectedValid, audioFile.IsValid);
                Assert.Equal(extension, audioFile.FileExtension);
            }
            finally
            {
                // Cleanup
                if (File.Exists(testPath))
                    File.Delete(testPath);
            }
        }

        [Fact]
        public void DisplayName_ShouldReturnFileNameWithExtension()
        {
            // Arrange
            var tempFile = Path.GetTempFileName();
            var csvPath = Path.ChangeExtension(tempFile, ".csv");
            File.Move(tempFile, csvPath);

            try
            {
                // Act
                var audioFile = AudioFile.FromPath(csvPath);
                var displayName = audioFile.DisplayName;

                // Assert
                Assert.Equal(Path.GetFileName(csvPath), displayName);
            }
            finally
            {
                // Cleanup
                if (File.Exists(csvPath))
                    File.Delete(csvPath);
            }
        }

        [Theory]
        [InlineData(1023, "1023 B")]
        [InlineData(1024, "1.0 KB")]
        [InlineData(1048576, "1.0 MB")]
        [InlineData(1073741824, "1.0 GB")]
        public void FileSizeDisplay_ShouldFormatCorrectly(long fileSize, string expected)
        {
            // Arrange
            var tempFile = Path.GetTempFileName();
            var csvPath = Path.ChangeExtension(tempFile, ".csv");
            File.Move(tempFile, csvPath);

            try
            {
                var audioFile = AudioFile.FromPath(csvPath);
                
                // Use reflection to set the file size for testing
                var fileSizeProperty = typeof(AudioFile).GetProperty(nameof(AudioFile.FileSize));
                fileSizeProperty?.SetValue(audioFile, fileSize);

                // Act
                var fileSizeDisplay = audioFile.FileSizeDisplay;

                // Assert
                Assert.Equal(expected, fileSizeDisplay);
            }
            finally
            {
                // Cleanup
                if (File.Exists(csvPath))
                    File.Delete(csvPath);
            }
        }
    }
}
