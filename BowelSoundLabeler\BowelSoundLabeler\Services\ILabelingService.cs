using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 标注服务接口
    /// </summary>
    public interface ILabelingService
    {
        /// <summary>
        /// 开始批量标注流程
        /// </summary>
        Task<BatchLabelingResult> StartBatchLabelingAsync(
            IList<AudioFile> files,
            IProgress<BatchLabelingProgress>? progress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 处理单个文件标注
        /// </summary>
        Task<FileLabelingInfo?> ProcessSingleFileLabelingAsync(AudioFile file);
    }

    /// <summary>
    /// 批量标注结果
    /// </summary>
    public class BatchLabelingResult
    {
        public int TotalFiles { get; set; }
        public int ProcessedFiles { get; set; }
        public int SkippedFiles { get; set; }
        public int ErrorFiles { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<FileLabelingInfo> ProcessedLabelings { get; set; } = new();
        public bool IsCompleted { get; set; }
        public bool IsCancelled { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// 批量标注进度
    /// </summary>
    public class BatchLabelingProgress
    {
        public int CurrentIndex { get; set; }
        public int TotalCount { get; set; }
        public string CurrentFileName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public LabelingStage Stage { get; set; }
        public double ProgressPercentage => TotalCount > 0 ? (double)CurrentIndex / TotalCount * 100 : 0;
    }

    /// <summary>
    /// 标注阶段
    /// </summary>
    public enum LabelingStage
    {
        Preparing,
        UserInput,
        Processing,
        Completed,
        Error
    }
}
