using System.Diagnostics;
using Microsoft.Extensions.Logging;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 标注服务实现
    /// </summary>
    public class LabelingService : ILabelingService
    {
        private readonly ILogger<LabelingService> _logger;
        private readonly IDialogService _dialogService;
        private readonly ICsvProcessingService _csvProcessingService;

        public LabelingService(
            ILogger<LabelingService> logger,
            IDialogService dialogService,
            ICsvProcessingService csvProcessingService)
        {
            _logger = logger;
            _dialogService = dialogService;
            _csvProcessingService = csvProcessingService;
        }

        public async Task<BatchLabelingResult> StartBatchLabelingAsync(
            IList<AudioFile> files,
            IProgress<BatchLabelingProgress>? progress = null,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new BatchLabelingResult
            {
                TotalFiles = files.Count
            };

            try
            {
                _logger.LogInformation("开始批量标注，共 {Count} 个文件", files.Count);

                for (int i = 0; i < files.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        result.IsCancelled = true;
                        break;
                    }

                    var file = files[i];
                    
                    // 报告进度 - 准备阶段
                    progress?.Report(new BatchLabelingProgress
                    {
                        CurrentIndex = i + 1,
                        TotalCount = files.Count,
                        CurrentFileName = file.DisplayName,
                        Status = "准备处理...",
                        Stage = LabelingStage.Preparing
                    });

                    try
                    {
                        // 处理单个文件标注
                        var labelingInfo = await ProcessSingleFileLabelingAsync(file);
                        
                        if (labelingInfo == null)
                        {
                            result.SkippedFiles++;
                            continue;
                        }

                        if (labelingInfo.Result == LabelingResult.Skip)
                        {
                            result.SkippedFiles++;
                            continue;
                        }

                        // 报告进度 - 处理阶段
                        progress?.Report(new BatchLabelingProgress
                        {
                            CurrentIndex = i + 1,
                            TotalCount = files.Count,
                            CurrentFileName = file.DisplayName,
                            Status = "正在重命名文件...",
                            Stage = LabelingStage.Processing
                        });

                        // 执行文件重命名
                        var success = await _csvProcessingService.ProcessCsvLabelingAsync(labelingInfo, true);
                        
                        if (success)
                        {
                            result.ProcessedFiles++;
                            result.ProcessedLabelings.Add(labelingInfo);
                            _logger.LogInformation("文件标注成功: {FileName}", file.DisplayName);
                        }
                        else
                        {
                            result.ErrorFiles++;
                            if (!string.IsNullOrEmpty(labelingInfo.ErrorMessage))
                            {
                                result.Errors.Add($"{file.DisplayName}: {labelingInfo.ErrorMessage}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        result.ErrorFiles++;
                        result.Errors.Add($"{file.DisplayName}: {ex.Message}");
                        _logger.LogError(ex, "处理文件标注时发生错误: {FilePath}", file.FilePath);
                        
                        progress?.Report(new BatchLabelingProgress
                        {
                            CurrentIndex = i + 1,
                            TotalCount = files.Count,
                            CurrentFileName = file.DisplayName,
                            Status = $"错误: {ex.Message}",
                            Stage = LabelingStage.Error
                        });
                    }
                }

                result.IsCompleted = !result.IsCancelled;
                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("批量标注完成: 处理 {Processed}, 跳过 {Skipped}, 错误 {Errors}, 耗时 {Duration}",
                    result.ProcessedFiles, result.SkippedFiles, result.ErrorFiles, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量标注过程中发生错误");
                result.Errors.Add($"批量标注失败: {ex.Message}");
                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        public async Task<FileLabelingInfo?> ProcessSingleFileLabelingAsync(AudioFile file)
        {
            try
            {
                // 询问是否包含肠鸣音
                var dialogResult = await _dialogService.ShowThreeChoiceAsync(
                    "肠鸣音标注",
                    $"文件: {file.DisplayName}\n\n此文件是否包含肠鸣音？",
                    "是", "否", "跳过");

                var labelingInfo = new FileLabelingInfo
                {
                    AudioFile = file
                };

                switch (dialogResult)
                {
                    case DialogResult.Option3: // 跳过
                    case DialogResult.Cancel:
                        labelingInfo.Result = LabelingResult.Skip;
                        return labelingInfo;

                    case DialogResult.Option2: // 否
                        labelingInfo.Result = LabelingResult.No;
                        labelingInfo.BowelSoundCount = 0;
                        return labelingInfo;

                    case DialogResult.Option1: // 是
                        // 询问肠鸣音数量
                        var countInput = await _dialogService.ShowInputAsync(
                            "肠鸣音数量",
                            "请输入肠鸣音的数量:",
                            "1");

                        if (string.IsNullOrEmpty(countInput))
                        {
                            labelingInfo.Result = LabelingResult.Skip;
                            return labelingInfo;
                        }

                        if (int.TryParse(countInput, out var count) && count >= 0)
                        {
                            labelingInfo.Result = LabelingResult.Yes;
                            labelingInfo.BowelSoundCount = count;
                            return labelingInfo;
                        }
                        else
                        {
                            await _dialogService.ShowErrorAsync("输入错误", "请输入有效的非负整数");
                            labelingInfo.Result = LabelingResult.Skip;
                            return labelingInfo;
                        }

                    default:
                        labelingInfo.Result = LabelingResult.Skip;
                        return labelingInfo;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理单个文件标注时发生错误: {FilePath}", file.FilePath);
                return null;
            }
        }
    }
}
