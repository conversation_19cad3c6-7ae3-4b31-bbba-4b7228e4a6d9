using Microsoft.Extensions.Logging;
using Windows.Storage;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 设置服务实现
    /// </summary>
    public class SettingsService : ISettingsService
    {
        private const string DefaultFolderKey = "DefaultFolder";
        private readonly ILogger<SettingsService> _logger;
        private readonly ApplicationDataContainer _localSettings;

        public SettingsService(ILogger<SettingsService> logger)
        {
            _logger = logger;
            _localSettings = ApplicationData.Current.LocalSettings;
        }

        public async Task<string?> GetDefaultFolderAsync()
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (_localSettings.Values.TryGetValue(DefaultFolderKey, out var value) && value is string folderPath)
                {
                    // 验证路径是否仍然存在
                    if (Directory.Exists(folderPath))
                    {
                        _logger.LogDebug("获取默认文件夹: {FolderPath}", folderPath);
                        return folderPath;
                    }
                    else
                    {
                        // 路径不存在，清除设置
                        _logger.LogWarning("默认文件夹不存在，清除设置: {FolderPath}", folderPath);
                        await ClearDefaultFolderAsync();
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认文件夹时发生错误");
                return null;
            }
        }

        public async Task SetDefaultFolderAsync(string folderPath)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (string.IsNullOrWhiteSpace(folderPath))
                {
                    throw new ArgumentException("文件夹路径不能为空", nameof(folderPath));
                }

                if (!Directory.Exists(folderPath))
                {
                    throw new DirectoryNotFoundException($"文件夹不存在: {folderPath}");
                }

                _localSettings.Values[DefaultFolderKey] = folderPath;
                _logger.LogInformation("设置默认文件夹: {FolderPath}", folderPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置默认文件夹时发生错误: {FolderPath}", folderPath);
                throw;
            }
        }

        public async Task ClearDefaultFolderAsync()
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (_localSettings.Values.ContainsKey(DefaultFolderKey))
                {
                    _localSettings.Values.Remove(DefaultFolderKey);
                    _logger.LogInformation("已清除默认文件夹设置");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除默认文件夹设置时发生错误");
                throw;
            }
        }

        public async Task<bool> IsDefaultFolderValidAsync()
        {
            try
            {
                var folderPath = await GetDefaultFolderAsync();
                return !string.IsNullOrEmpty(folderPath) && Directory.Exists(folderPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证默认文件夹时发生错误");
                return false;
            }
        }
    }
}
