﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using Microsoft.UI.Xaml.Shapes;
using Windows.ApplicationModel;
using Windows.ApplicationModel.Activation;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using BowelSoundLabeler.Services;
using BowelSoundLabeler.ViewModels;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace BowelSoundLabeler
{
    /// <summary>
    /// Provides application-specific behavior to supplement the default Application class.
    /// </summary>
    public partial class App : Application
    {
        private Window? _window;
        private IHost? _host;

        public static Window MainWindow { get; private set; } = null!;
        public static IServiceProvider Services => ((App)Current)._host?.Services ?? throw new InvalidOperationException("Services not initialized");

        /// <summary>
        /// Initializes the singleton application object.  This is the first line of authored code
        /// executed, and as such is the logical equivalent of main() or WinMain().
        /// </summary>
        public App()
        {
            InitializeComponent();

            // 设置全局异常处理
            this.UnhandledException += OnUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnDomainUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

            // 配置依赖注入
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices(ConfigureServices)
                .ConfigureLogging(logging =>
                {
                    logging.AddDebug();
                    logging.SetMinimumLevel(LogLevel.Information);
                })
                .Build();
        }

        /// <summary>
        /// 配置服务
        /// </summary>
        private void ConfigureServices(IServiceCollection services)
        {
            // 注册服务
            services.AddSingleton<ISettingsService, SettingsService>();
            services.AddSingleton<IFileService, FileService>();
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<ICsvProcessingService, CsvProcessingService>();
            services.AddSingleton<ILabelingService, LabelingService>();
            services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();

            // 注册ViewModels
            services.AddTransient<MainWindowViewModel>();
        }

        /// <summary>
        /// Invoked when the application is launched.
        /// </summary>
        /// <param name="args">Details about the launch request and process.</param>
        protected override void OnLaunched(Microsoft.UI.Xaml.LaunchActivatedEventArgs args)
        {
            _window = new MainWindow();
            MainWindow = _window;
            _window.Activate();
        }

        private async void OnUnhandledException(object sender, Microsoft.UI.Xaml.UnhandledExceptionEventArgs e)
        {
            try
            {
                var errorHandlingService = Services.GetService<IErrorHandlingService>();
                if (errorHandlingService != null)
                {
                    await errorHandlingService.HandleUnhandledExceptionAsync(e.Exception, "WinUI UnhandledException");
                }
                e.Handled = true;
            }
            catch
            {
                // 如果错误处理失败，让应用程序崩溃
            }
        }

        private async void OnDomainUnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            try
            {
                var errorHandlingService = Services.GetService<IErrorHandlingService>();
                if (errorHandlingService != null && e.ExceptionObject is Exception exception)
                {
                    await errorHandlingService.HandleUnhandledExceptionAsync(exception, "AppDomain UnhandledException");
                }
            }
            catch
            {
                // 如果错误处理失败，让应用程序崩溃
            }
        }

        private async void OnUnobservedTaskException(object? sender, System.Threading.Tasks.UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                var errorHandlingService = Services.GetService<IErrorHandlingService>();
                if (errorHandlingService != null)
                {
                    await errorHandlingService.HandleUnhandledExceptionAsync(e.Exception, "Task UnobservedException");
                }
                e.SetObserved();
            }
            catch
            {
                // 如果错误处理失败，让应用程序崩溃
            }
        }
    }
}
