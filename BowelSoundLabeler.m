function BowelSoundLabeler()
    % 肠鸣音标注器 - MATLAB GUI应用程序
    % 功能：对音频/信号文件进行肠鸣音标注并自动重命名
    % 作者：自动生成
    % 日期：2025-08-21
    % 更新：添加文件夹记忆功能

    % 偏好设置组名
    PREF_GROUP = 'BowelSoundLabeler';
    PREF_KEY = 'DefaultFolder';

    % 创建主窗口
    fig = uifigure('Name', '肠鸣音标注器', 'Position', [100, 100, 700, 500]);
    fig.Resize = 'off';

    % 创建网格布局 - 增加行数以容纳新功能
    grid = uigridlayout(fig, [8, 2]);
    grid.RowHeight = {'fit', 'fit', 'fit', 'fit', '1x', 'fit', 'fit', 'fit'};
    grid.ColumnWidth = {'1x', '1x'};
    
    % 标题
    titleLabel = uilabel(grid, 'Text', '肠鸣音标注器', 'FontSize', 18, 'FontWeight', 'bold');
    titleLabel.Layout.Row = 1;
    titleLabel.Layout.Column = [1, 2];
    titleLabel.HorizontalAlignment = 'center';

    % 说明文字
    infoLabel = uilabel(grid, 'Text', '选择音频/信号文件进行肠鸣音标注，系统将自动重命名文件');
    infoLabel.Layout.Row = 2;
    infoLabel.Layout.Column = [1, 2];
    infoLabel.HorizontalAlignment = 'center';

    % 默认文件夹设置区域
    folderSettingBtn = uibutton(grid, 'Text', '设置默认文件夹', 'ButtonPushedFcn', @(btn,event) setDefaultFolder());
    folderSettingBtn.Layout.Row = 3;
    folderSettingBtn.Layout.Column = 1;
    folderSettingBtn.BackgroundColor = [0.9, 0.9, 0.9];

    clearFolderBtn = uibutton(grid, 'Text', '清除默认文件夹', 'ButtonPushedFcn', @(btn,event) clearDefaultFolder());
    clearFolderBtn.Layout.Row = 3;
    clearFolderBtn.Layout.Column = 2;
    clearFolderBtn.BackgroundColor = [0.9, 0.9, 0.9];

    % 当前默认文件夹显示
    currentFolderLabel = uilabel(grid, 'Text', '当前默认文件夹: 未设置', 'FontSize', 10);
    currentFolderLabel.Layout.Row = 4;
    currentFolderLabel.Layout.Column = [1, 2];
    currentFolderLabel.HorizontalAlignment = 'center';
    currentFolderLabel.FontColor = [0.6, 0.6, 0.6];

    % 文件选择按钮区域
    selectSingleBtn = uibutton(grid, 'Text', '选择单个文件', 'ButtonPushedFcn', @(btn,event) selectSingleFile());
    selectSingleBtn.Layout.Row = 5;
    selectSingleBtn.Layout.Column = 1;

    selectMultipleBtn = uibutton(grid, 'Text', '选择多个文件', 'ButtonPushedFcn', @(btn,event) selectMultipleFiles());
    selectMultipleBtn.Layout.Row = 5;
    selectMultipleBtn.Layout.Column = 2;

    % 文件列表显示区域
    fileListArea = uitextarea(grid, 'Value', {'选择文件后将在此显示文件列表...'}, 'Editable', 'off');
    fileListArea.Layout.Row = 6;
    fileListArea.Layout.Column = [1, 2];

    % 开始标注按钮
    startLabelBtn = uibutton(grid, 'Text', '开始标注', 'ButtonPushedFcn', @(btn,event) startLabeling(), 'Enable', 'off');
    startLabelBtn.Layout.Row = 7;
    startLabelBtn.Layout.Column = [1, 2];
    startLabelBtn.BackgroundColor = [0.2, 0.7, 0.2];
    startLabelBtn.FontColor = 'white';
    startLabelBtn.FontWeight = 'bold';

    % 状态栏
    statusLabel = uilabel(grid, 'Text', '就绪', 'FontColor', [0.5, 0.5, 0.5]);
    statusLabel.Layout.Row = 8;
    statusLabel.Layout.Column = [1, 2];
    statusLabel.HorizontalAlignment = 'center';
    
    % 全局变量存储选中的文件
    selectedFiles = {};

    % 初始化默认文件夹显示
    updateCurrentFolderDisplay();

    % 获取保存的默认文件夹路径
    function defaultPath = getDefaultFolder()
        try
            if ispref(PREF_GROUP, PREF_KEY)
                defaultPath = getpref(PREF_GROUP, PREF_KEY);
                % 验证路径是否仍然存在
                if ~exist(defaultPath, 'dir')
                    % 如果路径不存在，清除偏好设置
                    rmpref(PREF_GROUP, PREF_KEY);
                    defaultPath = pwd;
                end
            else
                defaultPath = pwd;
            end
        catch
            defaultPath = pwd;
        end
    end

    % 保存默认文件夹路径
    function saveDefaultFolder(folderPath)
        try
            setpref(PREF_GROUP, PREF_KEY, folderPath);
        catch ME
            uialert(fig, ['保存默认文件夹失败: ' ME.message], '错误');
        end
    end

    % 设置默认文件夹
    function setDefaultFolder()
        try
            currentDefault = getDefaultFolder();
            selectedFolder = uigetdir(currentDefault, '选择默认工作文件夹');

            if selectedFolder ~= 0
                saveDefaultFolder(selectedFolder);
                updateCurrentFolderDisplay();
                statusLabel.Text = '默认文件夹已更新';
            end
        catch ME
            uialert(fig, ['设置默认文件夹时出错: ' ME.message], '错误');
        end
    end

    % 清除默认文件夹设置
    function clearDefaultFolder()
        try
            if ispref(PREF_GROUP, PREF_KEY)
                rmpref(PREF_GROUP, PREF_KEY);
                updateCurrentFolderDisplay();
                statusLabel.Text = '默认文件夹设置已清除';
            else
                uialert(fig, '没有设置默认文件夹', '提示');
            end
        catch ME
            uialert(fig, ['清除默认文件夹时出错: ' ME.message], '错误');
        end
    end

    % 更新当前文件夹显示
    function updateCurrentFolderDisplay()
        try
            defaultPath = getDefaultFolder();
            if ispref(PREF_GROUP, PREF_KEY)
                % 显示路径，如果太长则截断
                if length(defaultPath) > 60
                    displayPath = ['...' defaultPath(end-56:end)];
                else
                    displayPath = defaultPath;
                end
                currentFolderLabel.Text = ['当前默认文件夹: ' displayPath];
                currentFolderLabel.FontColor = [0.2, 0.6, 0.2];
            else
                currentFolderLabel.Text = '当前默认文件夹: 未设置';
                currentFolderLabel.FontColor = [0.6, 0.6, 0.6];
            end
        catch
            currentFolderLabel.Text = '当前默认文件夹: 获取失败';
            currentFolderLabel.FontColor = [0.8, 0.2, 0.2];
        end
    end

    % 选择单个文件函数
    function selectSingleFile()
        try
            defaultPath = getDefaultFolder();
            [filename, pathname] = uigetfile({'*.mat;*.wav;*.mp3;*.m4a;*.flac', '音频/信号文件 (*.mat,*.wav,*.mp3,*.m4a,*.flac)'; ...
                                            '*.mat', 'MATLAB文件 (*.mat)'; ...
                                            '*.wav', 'WAV文件 (*.wav)'; ...
                                            '*.mp3', 'MP3文件 (*.mp3)'; ...
                                            '*.m4a', 'M4A文件 (*.m4a)'; ...
                                            '*.flac', 'FLAC文件 (*.flac)'; ...
                                            '*.*', '所有文件 (*.*)'}, ...
                                            '选择音频/信号文件', defaultPath);

            if filename ~= 0
                selectedFiles = {fullfile(pathname, filename)};
                % 保存用户选择的文件夹作为新的默认路径
                saveDefaultFolder(pathname);
                updateCurrentFolderDisplay();
                updateFileList();
                startLabelBtn.Enable = 'on';
                statusLabel.Text = '已选择1个文件';
            end
        catch ME
            uialert(fig, ['选择文件时出错: ' ME.message], '错误');
        end
    end
    
    % 选择多个文件函数
    function selectMultipleFiles()
        try
            defaultPath = getDefaultFolder();
            [filenames, pathname] = uigetfile({'*.mat;*.wav;*.mp3;*.m4a;*.flac', '音频/信号文件 (*.mat,*.wav,*.mp3,*.m4a,*.flac)'; ...
                                             '*.mat', 'MATLAB文件 (*.mat)'; ...
                                             '*.wav', 'WAV文件 (*.wav)'; ...
                                             '*.mp3', 'MP3文件 (*.mp3)'; ...
                                             '*.m4a', 'M4A文件 (*.m4a)'; ...
                                             '*.flac', 'FLAC文件 (*.flac)'; ...
                                             '*.*', '所有文件 (*.*)'}, ...
                                             '选择音频/信号文件', defaultPath, 'MultiSelect', 'on');

            if ~isequal(filenames, 0)
                if ischar(filenames)
                    filenames = {filenames};
                end
                selectedFiles = cellfun(@(x) fullfile(pathname, x), filenames, 'UniformOutput', false);
                % 保存用户选择的文件夹作为新的默认路径
                saveDefaultFolder(pathname);
                updateCurrentFolderDisplay();
                updateFileList();
                startLabelBtn.Enable = 'on';
                statusLabel.Text = sprintf('已选择%d个文件', length(selectedFiles));
            end
        catch ME
            uialert(fig, ['选择文件时出错: ' ME.message], '错误');
        end
    end
    
    % 更新文件列表显示
    function updateFileList()
        if isempty(selectedFiles)
            fileListArea.Value = {'选择文件后将在此显示文件列表...'};
        else
            fileList = cell(length(selectedFiles), 1);
            for i = 1:length(selectedFiles)
                [~, name, ext] = fileparts(selectedFiles{i});
                fileList{i} = sprintf('%d. %s%s', i, name, ext);
            end
            fileListArea.Value = fileList;
        end
    end
    
    % 开始标注函数
    function startLabeling()
        if isempty(selectedFiles)
            uialert(fig, '请先选择文件', '提示');
            return;
        end
        
        % 禁用按钮防止重复操作
        startLabelBtn.Enable = 'off';
        selectSingleBtn.Enable = 'off';
        selectMultipleBtn.Enable = 'off';
        folderSettingBtn.Enable = 'off';
        clearFolderBtn.Enable = 'off';
        
        processedCount = 0;
        skippedCount = 0;
        errorCount = 0;
        
        try
            for i = 1:length(selectedFiles)
                currentFile = selectedFiles{i};
                [filepath, filename, ext] = fileparts(currentFile);
                
                % 更新状态
                statusLabel.Text = sprintf('正在处理文件 %d/%d: %s%s', i, length(selectedFiles), filename, ext);
                drawnow;
                
                % 询问是否包含肠鸣音
                answer = questdlg(sprintf('文件: %s%s\n\n此文件是否包含肠鸣音？', filename, ext), ...
                                '肠鸣音标注', '是', '否', '跳过', '否');
                
                if strcmp(answer, '跳过') || isempty(answer)
                    skippedCount = skippedCount + 1;
                    continue;
                end
                
                try
                    if strcmp(answer, '否')
                        % 无肠鸣音，添加_no后缀
                        newFilename = [filename '_no' ext];
                        newFilePath = fullfile(filepath, newFilename);
                        
                        % 检查文件是否已存在
                        if exist(newFilePath, 'file')
                            choice = questdlg(sprintf('文件 %s 已存在，是否覆盖？', newFilename), ...
                                            '文件已存在', '覆盖', '跳过', '跳过');
                            if ~strcmp(choice, '覆盖')
                                skippedCount = skippedCount + 1;
                                continue;
                            end
                        end
                        
                        % 重命名文件
                        movefile(currentFile, newFilePath);
                        processedCount = processedCount + 1;
                        
                    elseif strcmp(answer, '是')
                        % 有肠鸣音，询问数量
                        countStr = inputdlg('请输入肠鸣音的数量:', '肠鸣音数量', 1, {'1'});
                        
                        if isempty(countStr) || isempty(countStr{1})
                            skippedCount = skippedCount + 1;
                            continue;
                        end
                        
                        count = str2double(countStr{1});
                        if isnan(count) || count < 0 || count ~= round(count)
                            uialert(fig, '请输入有效的非负整数', '输入错误');
                            skippedCount = skippedCount + 1;
                            continue;
                        end
                        
                        % 添加_yes_N后缀
                        newFilename = sprintf('%s_yes_%d%s', filename, count, ext);
                        newFilePath = fullfile(filepath, newFilename);
                        
                        % 检查文件是否已存在
                        if exist(newFilePath, 'file')
                            choice = questdlg(sprintf('文件 %s 已存在，是否覆盖？', newFilename), ...
                                            '文件已存在', '覆盖', '跳过', '跳过');
                            if ~strcmp(choice, '覆盖')
                                skippedCount = skippedCount + 1;
                                continue;
                            end
                        end
                        
                        % 重命名文件
                        movefile(currentFile, newFilePath);
                        processedCount = processedCount + 1;
                    end
                    
                catch ME
                    errorCount = errorCount + 1;
                    uialert(fig, sprintf('处理文件 %s%s 时出错: %s', filename, ext, ME.message), '错误');
                end
            end
            
            % 显示完成信息
            resultMsg = sprintf('标注完成！\n\n处理成功: %d 个文件\n跳过: %d 个文件\n错误: %d 个文件', ...
                              processedCount, skippedCount, errorCount);
            uialert(fig, resultMsg, '完成', 'Icon', 'success');
            
        catch ME
            uialert(fig, ['标注过程中出现错误: ' ME.message], '错误');
        end
        
        % 重新启用按钮
        startLabelBtn.Enable = 'on';
        selectSingleBtn.Enable = 'on';
        selectMultipleBtn.Enable = 'on';
        folderSettingBtn.Enable = 'on';
        clearFolderBtn.Enable = 'on';
        
        % 清空文件列表
        selectedFiles = {};
        updateFileList();
        startLabelBtn.Enable = 'off';
        statusLabel.Text = '标注完成，就绪';
    end
end
