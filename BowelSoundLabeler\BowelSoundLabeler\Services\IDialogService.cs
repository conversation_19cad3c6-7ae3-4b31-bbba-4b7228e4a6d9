namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 对话框服务接口
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// 选择文件夹
        /// </summary>
        Task<string?> SelectFolderAsync(string? initialFolder = null);

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        Task<bool> ShowConfirmationAsync(string title, string message);

        /// <summary>
        /// 显示信息对话框
        /// </summary>
        Task ShowInformationAsync(string title, string message);

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        Task ShowErrorAsync(string title, string message);

        /// <summary>
        /// 显示输入对话框
        /// </summary>
        Task<string?> ShowInputAsync(string title, string message, string defaultValue = "");

        /// <summary>
        /// 显示三选一对话框（是/否/跳过）
        /// </summary>
        Task<DialogResult> ShowThreeChoiceAsync(string title, string message, string option1 = "是", string option2 = "否", string option3 = "跳过");
    }

    /// <summary>
    /// 对话框结果枚举
    /// </summary>
    public enum DialogResult
    {
        Option1,
        Option2,
        Option3,
        Cancel
    }
}
