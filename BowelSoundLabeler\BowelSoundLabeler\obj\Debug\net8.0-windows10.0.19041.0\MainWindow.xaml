﻿<?xml version="1.0" encoding="utf-8"?>
<Window x:ConnectionId='1'
    x:Class="BowelSoundLabeler.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:BowelSoundLabeler"
    xmlns:models="using:BowelSoundLabeler.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="BowelSoundLabeler">

    <Window.SystemBackdrop>
        <MicaBackdrop />
    </Window.SystemBackdrop>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Grid.ColumnSpan="2"
                   Text="肠鸣音标注器"
                   FontSize="24"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="20,20,20,10"/>

        <!-- 说明文字 -->
        <TextBlock Grid.Row="1" Grid.ColumnSpan="2"
                   Text="选择数据文件进行肠鸣音标注，系统将自动重命名文件"
                   HorizontalAlignment="Center"
                   Margin="20,0,20,20"
                   Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>

        <!-- 默认文件夹设置区域 -->
        <Button x:ConnectionId='2' Grid.Row="2" Grid.Column="0"
                Content="设置默认文件夹"
                                                                    
                Margin="20,10,10,10"
                HorizontalAlignment="Stretch"/>

        <Button x:ConnectionId='3' Grid.Row="2" Grid.Column="1"
                Content="清除默认文件夹"
                                                                      
                Margin="10,10,20,10"
                HorizontalAlignment="Stretch"/>

        <!-- 当前默认文件夹显示 -->
        <TextBlock x:ConnectionId='4' Grid.Row="3" Grid.ColumnSpan="2"
                                                                           
                   HorizontalAlignment="Center"
                   Margin="20,0,20,10"
                   FontSize="12"
                   Foreground="{ThemeResource TextFillColorTertiaryBrush}"/>

        <!-- 文件选择按钮区域 -->
        <StackPanel Grid.Row="4" Grid.ColumnSpan="2" Orientation="Horizontal"
                    HorizontalAlignment="Center" Margin="20,10,20,10">
            <Button x:ConnectionId='14' Content="选择单个文件"
                                                                        
                    Margin="0,0,10,0"
                    MinWidth="120"/>
            <Button x:ConnectionId='15' Content="选择多个文件"
                                                                           
                    Margin="10,0,0,0"
                    MinWidth="120"/>
        </StackPanel>

        <!-- 文件列表显示区域 -->
        <Border Grid.Row="5" Grid.ColumnSpan="2"
                Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Margin="20,10,20,10"
                MinHeight="200">
            <ScrollViewer>
                <ListView x:ConnectionId='8'                                                            
                          SelectionMode="None">
                    <ListView.ItemTemplate>
                        <DataTemplate                              >
                            <Grid x:ConnectionId='10' Margin="10,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock x:ConnectionId='12'                            
                                               FontWeight="SemiBold"/>
                                    <TextBlock x:ConnectionId='13'                                
                                               FontSize="12"
                                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                                </StackPanel>

                                <TextBlock x:ConnectionId='11' Grid.Column="1"
                                                                        
                                           VerticalAlignment="Center"
                                           Margin="10,0"
                                           FontFamily="Consolas"
                                           Foreground="{ThemeResource AccentTextFillColorPrimaryBrush}"/>

                                <Button Grid.Column="2"
                                        Content="移除"
                                        FontSize="12"
                                        Padding="8,4"/>
                            </Grid>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>
            </ScrollViewer>
        </Border>

        <!-- 开始标注按钮 -->
        <Button x:ConnectionId='5' Grid.Row="6" Grid.ColumnSpan="2"
                Content="开始标注"
                                                                 
                                                                            
                HorizontalAlignment="Center"
                Margin="20,10,20,10"
                MinWidth="200"
                Style="{ThemeResource AccentButtonStyle}"/>

        <!-- 进度条 -->
        <ProgressBar x:ConnectionId='6' Grid.Row="7" Grid.ColumnSpan="2"
                                                                               
                                                                              
                     Margin="20,5,20,5"
                     Height="4"/>

        <!-- 状态栏 -->
        <TextBlock x:ConnectionId='7' Grid.Row="7" Grid.ColumnSpan="2"
                                                                    
                   HorizontalAlignment="Center"
                   Margin="20,10,20,20"
                   Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
    </Grid>
</Window>

