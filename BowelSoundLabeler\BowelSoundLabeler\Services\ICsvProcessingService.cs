using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// CSV文件处理服务接口
    /// </summary>
    public interface ICsvProcessingService
    {
        /// <summary>
        /// 验证CSV文件格式
        /// </summary>
        Task<bool> ValidateCsvFileAsync(AudioFile csvFile);

        /// <summary>
        /// 获取CSV文件信息
        /// </summary>
        Task<CsvFileInfo?> GetCsvInfoAsync(AudioFile csvFile);

        /// <summary>
        /// 读取CSV文件内容预览
        /// </summary>
        Task<CsvPreview?> GetCsvPreviewAsync(AudioFile csvFile, int maxRows = 10);

        /// <summary>
        /// 处理单个CSV文件标注
        /// </summary>
        Task<bool> ProcessCsvLabelingAsync(FileLabelingInfo labelingInfo, bool overwrite = false);

        /// <summary>
        /// 批量处理CSV文件标注
        /// </summary>
        Task<BatchProcessingResult> ProcessBatchCsvLabelingAsync(
            IList<FileLabelingInfo> labelingInfos, 
            IProgress<BatchProcessingProgress>? progress = null,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// CSV文件信息
    /// </summary>
    public class CsvFileInfo
    {
        public int RowCount { get; set; }
        public int ColumnCount { get; set; }
        public List<string> Headers { get; set; } = new();
        public string Encoding { get; set; } = string.Empty;
        public char Delimiter { get; set; } = ',';
        public bool HasHeaders { get; set; }
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// CSV文件预览
    /// </summary>
    public class CsvPreview
    {
        public List<string> Headers { get; set; } = new();
        public List<List<string>> Rows { get; set; } = new();
        public int TotalRows { get; set; }
        public bool HasMoreRows { get; set; }
    }


}
