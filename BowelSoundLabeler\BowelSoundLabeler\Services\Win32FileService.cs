using System.Runtime.InteropServices;
using System.Text;
using Microsoft.Extensions.Logging;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 使用Win32 API的文件服务，支持设置初始文件夹
    /// </summary>
    public class Win32FileService : IFileService
    {
        private readonly ILogger<Win32FileService> _logger;
        private readonly IReadOnlyList<string> _supportedExtensions = new[]
        {
            ".wav", ".mp3", ".m4a", ".flac", ".mat", ".csv"
        };

        [DllImport("comdlg32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern bool GetOpenFileName(ref OPENFILENAME ofn);

        [DllImport("comdlg32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern bool GetSaveFileName(ref OPENFILENAME ofn);

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct OPENFILENAME
        {
            public int lStructSize;
            public IntPtr hwndOwner;
            public IntPtr hInstance;
            public string lpstrFilter;
            public string lpstrCustomFilter;
            public int nMaxCustFilter;
            public int nFilterIndex;
            public string lpstrFile;
            public int nMaxFile;
            public string lpstrFileTitle;
            public int nMaxFileTitle;
            public string lpstrInitialDir;
            public string lpstrTitle;
            public int Flags;
            public short nFileOffset;
            public short nFileExtension;
            public string lpstrDefExt;
            public IntPtr lCustData;
            public IntPtr lpfnHook;
            public string lpTemplateName;
        }

        private const int OFN_ALLOWMULTISELECT = 0x00000200;
        private const int OFN_EXPLORER = 0x00080000;
        private const int OFN_FILEMUSTEXIST = 0x00001000;
        private const int OFN_PATHMUSTEXIST = 0x00000800;

        public Win32FileService(ILogger<Win32FileService> logger)
        {
            _logger = logger;
        }

        public async Task<AudioFile?> SelectSingleFileAsync(string? initialFolder = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var ofn = new OPENFILENAME();
                    ofn.lStructSize = Marshal.SizeOf(ofn);
                    ofn.hwndOwner = GetActiveWindow();
                    
                    // 设置文件过滤器
                    var filter = CreateFileFilter();
                    ofn.lpstrFilter = filter;
                    ofn.nFilterIndex = 1;
                    
                    // 设置初始文件夹
                    if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                    {
                        ofn.lpstrInitialDir = initialFolder;
                    }
                    
                    // 设置文件名缓冲区
                    var fileName = new StringBuilder(260);
                    ofn.lpstrFile = fileName.ToString();
                    ofn.nMaxFile = fileName.Capacity;
                    
                    ofn.lpstrTitle = "选择文件";
                    ofn.Flags = OFN_FILEMUSTEXIST | OFN_PATHMUSTEXIST | OFN_EXPLORER;
                    
                    if (GetOpenFileName(ref ofn))
                    {
                        var selectedFile = ofn.lpstrFile;
                        if (!string.IsNullOrEmpty(selectedFile) && File.Exists(selectedFile))
                        {
                            var audioFile = AudioFile.FromPath(selectedFile);
                            _logger.LogInformation("选择了文件: {FilePath}", audioFile.FilePath);
                            return audioFile;
                        }
                    }
                    
                    return null;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "使用Win32 API选择单个文件时发生错误");
                    return null;
                }
            });
        }

        public async Task<IList<AudioFile>> SelectMultipleFilesAsync(string? initialFolder = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var ofn = new OPENFILENAME();
                    ofn.lStructSize = Marshal.SizeOf(ofn);
                    ofn.hwndOwner = GetActiveWindow();
                    
                    // 设置文件过滤器
                    var filter = CreateFileFilter();
                    ofn.lpstrFilter = filter;
                    ofn.nFilterIndex = 1;
                    
                    // 设置初始文件夹
                    if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                    {
                        ofn.lpstrInitialDir = initialFolder;
                    }
                    
                    // 设置文件名缓冲区（多选需要更大的缓冲区）
                    var fileName = new StringBuilder(8192);
                    ofn.lpstrFile = fileName.ToString();
                    ofn.nMaxFile = fileName.Capacity;
                    
                    ofn.lpstrTitle = "选择多个文件";
                    ofn.Flags = OFN_ALLOWMULTISELECT | OFN_FILEMUSTEXIST | OFN_PATHMUSTEXIST | OFN_EXPLORER;
                    
                    if (GetOpenFileName(ref ofn))
                    {
                        var result = ParseMultipleFiles(ofn.lpstrFile);
                        var audioFiles = new List<AudioFile>();
                        
                        foreach (var filePath in result)
                        {
                            if (File.Exists(filePath))
                            {
                                var audioFile = AudioFile.FromPath(filePath);
                                audioFiles.Add(audioFile);
                            }
                        }
                        
                        _logger.LogInformation("选择了 {Count} 个文件", audioFiles.Count);
                        return audioFiles;
                    }
                    
                    return new List<AudioFile>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "使用Win32 API选择多个文件时发生错误");
                    return new List<AudioFile>();
                }
            });
        }

        public async Task<bool> ValidateAudioFileAsync(AudioFile audioFile)
        {
            try
            {
                await Task.CompletedTask;
                
                if (!File.Exists(audioFile.FilePath))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "文件不存在";
                    return false;
                }

                if (!_supportedExtensions.Contains(audioFile.FileExtension, StringComparer.OrdinalIgnoreCase))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "不支持的文件格式";
                    return false;
                }

                audioFile.IsValid = true;
                audioFile.ErrorMessage = null;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证文件时发生错误: {FilePath}", audioFile.FilePath);
                audioFile.IsValid = false;
                audioFile.ErrorMessage = ex.Message;
                return false;
            }
        }

        public async Task<bool> RenameFileAsync(string oldPath, string newPath, bool overwrite = false)
        {
            try
            {
                await Task.CompletedTask;
                
                if (!File.Exists(oldPath))
                {
                    throw new FileNotFoundException($"源文件不存在: {oldPath}");
                }

                if (File.Exists(newPath) && !overwrite)
                {
                    throw new InvalidOperationException($"目标文件已存在: {newPath}");
                }

                File.Move(oldPath, newPath, overwrite);
                _logger.LogInformation("文件重命名成功: {OldPath} -> {NewPath}", oldPath, newPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名文件时发生错误: {OldPath} -> {NewPath}", oldPath, newPath);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            await Task.CompletedTask;
            return File.Exists(filePath);
        }

        public IReadOnlyList<string> GetSupportedExtensions()
        {
            return _supportedExtensions;
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetActiveWindow();

        private string CreateFileFilter()
        {
            var filter = new StringBuilder();
            filter.Append("支持的文件|");
            filter.Append(string.Join(";", _supportedExtensions.Select(ext => $"*{ext}")));
            filter.Append("|所有文件|*.*");
            filter.Append('\0');
            return filter.ToString();
        }

        private List<string> ParseMultipleFiles(string fileString)
        {
            var files = new List<string>();
            var parts = fileString.Split('\0', StringSplitOptions.RemoveEmptyEntries);
            
            if (parts.Length == 1)
            {
                // 只选择了一个文件
                files.Add(parts[0]);
            }
            else if (parts.Length > 1)
            {
                // 选择了多个文件，第一个是文件夹路径
                var folder = parts[0];
                for (int i = 1; i < parts.Length; i++)
                {
                    files.Add(Path.Combine(folder, parts[i]));
                }
            }
            
            return files;
        }
    }
}
