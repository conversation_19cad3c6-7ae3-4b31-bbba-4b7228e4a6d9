using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 文件服务接口
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// 选择单个文件
        /// </summary>
        Task<AudioFile?> SelectSingleFileAsync(string? initialFolder = null);

        /// <summary>
        /// 选择多个文件
        /// </summary>
        Task<IList<AudioFile>> SelectMultipleFilesAsync(string? initialFolder = null);

        /// <summary>
        /// 验证音频文件
        /// </summary>
        Task<bool> ValidateAudioFileAsync(AudioFile audioFile);

        /// <summary>
        /// 重命名文件
        /// </summary>
        Task<bool> RenameFileAsync(string oldPath, string newPath, bool overwrite = false);

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        Task<bool> FileExistsAsync(string filePath);

        /// <summary>
        /// 获取支持的音频文件扩展名
        /// </summary>
        IReadOnlyList<string> GetSupportedExtensions();
    }
}
