# 肠鸣音标注器 (BowelSoundLabeler)

一个现代化的Windows桌面应用程序，用于对音频/数据文件进行肠鸣音标注并自动重命名文件。该应用程序使用WinUI 3构建，完全替代了原有的MATLAB脚本功能。

## 功能特性

### 核心功能
- **文件选择**: 支持单个和批量文件选择
- **多格式支持**: 支持 .csv, .wav, .mp3, .m4a, .flac, .mat 文件格式
- **智能标注**: 交互式标注工作流，支持肠鸣音数量记录
- **自动重命名**: 根据标注结果自动重命名文件
  - 无肠鸣音: `filename_no.ext`
  - 有肠鸣音: `filename_yes_N.ext` (N为数量)

### 用户体验
- **现代化界面**: 使用WinUI 3和Fluent Design设计语言
- **默认文件夹记忆**: 自动记住用户常用的工作文件夹
- **进度显示**: 批量处理时显示实时进度
- **错误处理**: 完善的错误处理和用户友好的错误提示
- **操作日志**: 自动记录操作历史和错误日志

## 技术架构

### 框架和技术栈
- **.NET 8.0**: 最新的.NET框架
- **WinUI 3**: 现代Windows应用UI框架
- **MVVM模式**: 使用CommunityToolkit.Mvvm实现
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **日志系统**: Microsoft.Extensions.Logging

### 项目结构
```
BowelSoundLabeler/
├── Models/                 # 数据模型
│   ├── AudioFile.cs       # 文件模型
│   └── LabelingResult.cs  # 标注结果模型
├── Services/              # 业务服务
│   ├── ISettingsService.cs      # 设置服务接口
│   ├── SettingsService.cs       # 设置服务实现
│   ├── IFileService.cs          # 文件服务接口
│   ├── FileService.cs           # 文件服务实现
│   ├── IDialogService.cs        # 对话框服务接口
│   ├── DialogService.cs         # 对话框服务实现
│   ├── ICsvProcessingService.cs # CSV处理服务接口
│   ├── CsvProcessingService.cs  # CSV处理服务实现
│   ├── ILabelingService.cs      # 标注服务接口
│   ├── LabelingService.cs       # 标注服务实现
│   ├── IErrorHandlingService.cs # 错误处理服务接口
│   └── ErrorHandlingService.cs  # 错误处理服务实现
├── ViewModels/            # 视图模型
│   └── MainWindowViewModel.cs   # 主窗口视图模型
├── Views/                 # 视图
│   ├── MainWindow.xaml           # 主窗口XAML
│   └── MainWindow.xaml.cs        # 主窗口代码后置
└── App.xaml/App.xaml.cs   # 应用程序入口
```

## 安装和运行

### 系统要求
- Windows 10 版本1809 (10.0.17763.0) 或更高版本
- .NET 8.0 Runtime

### 构建项目
```bash
# 克隆项目
git clone <repository-url>
cd BowelSoundLabeler

# 还原NuGet包
dotnet restore

# 构建项目
dotnet build

# 运行应用程序
dotnet run --project BowelSoundLabeler
```

### 发布应用程序
```bash
# 发布为自包含应用程序
dotnet publish -c Release -r win-x64 --self-contained true

# 发布为框架依赖应用程序
dotnet publish -c Release -r win-x64 --self-contained false
```

## 使用说明

1. **设置默认文件夹** (可选)
   - 点击"设置默认文件夹"按钮选择常用的工作目录
   - 应用程序会记住此设置，下次启动时自动使用

2. **选择文件**
   - 点击"选择单个文件"选择一个文件
   - 点击"选择多个文件"进行批量选择
   - 支持的格式: .csv, .wav, .mp3, .m4a, .flac, .mat

3. **开始标注**
   - 点击"开始标注"按钮启动标注流程
   - 对每个文件，系统会询问是否包含肠鸣音
   - 如果包含，需要输入肠鸣音的数量
   - 可以选择"跳过"来跳过某个文件

4. **查看结果**
   - 标注完成后会显示处理结果统计
   - 文件会根据标注结果自动重命名
   - 错误信息会保存到日志文件中

## 与MATLAB版本的对比

| 功能 | MATLAB版本 | WinUI 3版本 |
|------|------------|-------------|
| 用户界面 | 基础MATLAB GUI | 现代化WinUI 3界面 |
| 文件格式支持 | ✅ | ✅ (增强) |
| 批量处理 | ✅ | ✅ (带进度显示) |
| 错误处理 | 基础 | 完善的错误处理和日志 |
| 设置记忆 | MATLAB偏好设置 | Windows应用数据存储 |
| 部署要求 | 需要MATLAB | 独立Windows应用 |
| 性能 | 依赖MATLAB | 原生.NET性能 |

## 开发和测试

### 运行测试
```bash
# 运行单元测试
dotnet test BowelSoundLabeler.Tests

# 运行测试并生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

### 调试
- 使用Visual Studio 2022或Visual Studio Code
- 设置断点并使用F5启动调试
- 查看输出窗口中的日志信息

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0 (2025-08-24)
- 初始版本发布
- 完整的MATLAB脚本功能移植
- 现代化WinUI 3用户界面
- 完善的错误处理和日志系统
- 单元测试覆盖
